#!/usr/bin/env node

/**
 * Initialize System Settings
 * This script creates default system settings in the database
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const defaultSettings = {
  companyName: 'PrepLocus',
  companyDescription: 'India\'s leading online coaching platform',
  companyMission: 'Empowering students to achieve their dreams through quality education',
  companyVision: 'To be the most trusted educational platform in India',
  contactEmail: '<EMAIL>',
  contactPhone: '+91 98765 43210',
  contactAddress: 'New Delhi, India',
  supportEmail: '<EMAIL>',
  privacyEmail: '<EMAIL>',
  legalEmail: '<EMAIL>',
  youtubeChannel: 'https://www.youtube.com/@YourChannelName',
  facebookPage: '',
  twitterHandle: '',
  instagramHandle: '',
  linkedinPage: '',
  logoUrl: '',
  faviconUrl: '',
  brandColor: '#8B5CF6',
  siteUrl: 'http://localhost:3000',
  timezone: 'Asia/Kolkata',
  language: 'en',
  businessHours: 'Mon-Fri, 9 AM - 6 PM IST',
  teamMembers: [
    {
      id: '1',
      name: '<PERSON>esh <PERSON>',
      title: 'Founder & CEO',
      bio: 'Former IIT graduate with 15+ years in education technology. Passionate about making quality education accessible to every student in India.',
      image: '/images/team/founder.jpg',
      expertise: ['Education Technology', 'Strategic Planning', 'Team Leadership']
    },
    {
      id: '2',
      name: 'Priya Sharma',
      title: 'Co-Founder & CTO',
      bio: 'Ex-Google engineer with expertise in AI and machine learning. Leading the technical innovation at PrepLocus to create personalized learning experiences.',
      image: '/images/team/cofounder.jpg',
      expertise: ['Artificial Intelligence', 'Software Architecture', 'Product Development']
    }
  ],
  totalStudents: 500000,
  successRate: 98,
  coursesOffered: 50
};

async function initializeSystemSettings() {
  try {
    console.log('🚀 Initializing system settings...');

    // Check if settings already exist
    const existingSettings = await prisma.systemSetting.findMany({
      where: { category: 'company' }
    });

    if (existingSettings.length > 0) {
      console.log('⚠️ System settings already exist. Skipping initialization.');
      console.log(`Found ${existingSettings.length} existing settings.`);
      return;
    }

    // Create settings
    const settingsToCreate = Object.entries(defaultSettings).map(([key, value]) => ({
      key: `company.${key}`,
      value: typeof value === 'object' ? JSON.stringify(value) : String(value),
      category: 'company',
      description: `Company ${key}`
    }));

    await prisma.systemSetting.createMany({
      data: settingsToCreate
    });

    console.log('✅ System settings initialized successfully!');
    console.log(`Created ${settingsToCreate.length} settings entries.`);

    // Display created settings
    console.log('\n📋 Created Settings:');
    Object.entries(defaultSettings).forEach(([key, value]) => {
      if (typeof value === 'object') {
        console.log(`  ${key}: [Object/Array]`);
      } else {
        console.log(`  ${key}: ${value}`);
      }
    });

  } catch (error) {
    console.error('❌ Error initializing system settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function resetSystemSettings() {
  try {
    console.log('🔄 Resetting system settings...');

    // Delete existing settings
    const deleted = await prisma.systemSetting.deleteMany({
      where: { category: 'company' }
    });

    console.log(`🗑️ Deleted ${deleted.count} existing settings.`);

    // Create new settings
    const settingsToCreate = Object.entries(defaultSettings).map(([key, value]) => ({
      key: `company.${key}`,
      value: typeof value === 'object' ? JSON.stringify(value) : String(value),
      category: 'company',
      description: `Company ${key}`
    }));

    await prisma.systemSetting.createMany({
      data: settingsToCreate
    });

    console.log('✅ System settings reset successfully!');
    console.log(`Created ${settingsToCreate.length} new settings entries.`);

  } catch (error) {
    console.error('❌ Error resetting system settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'init':
    initializeSystemSettings();
    break;
  case 'reset':
    resetSystemSettings();
    break;
  default:
    console.log('📖 Usage:');
    console.log('  node scripts/init-system-settings.js init   - Initialize default settings');
    console.log('  node scripts/init-system-settings.js reset  - Reset settings to defaults');
    break;
}
