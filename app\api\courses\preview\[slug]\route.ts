import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'


// GET /api/courses/preview/[slug] - Get course preview details (public access)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const resolvedParams = await params
    const courseSlug = resolvedParams?.slug as string

    if (!courseSlug) {
      return APIResponse.error('Course slug is required', 400)
    }

    // Get session to check if user is enrolled (optional)
    const session = await auth()
    const userId = session?.user?.id

    // Find course by slug first, then by ID as fallback
    let course = await prisma.course.findFirst({
      where: {
        slug: courseSlug,
        isActive: true,
        isPublished: true
      },
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true,
            bio: true,
            _count: {
              select: {
                instructedCourses: {
                  where: {
                    isActive: true,
                    isPublished: true
                  }
                },
                courseEnrollments: true
              }
            }
          }
        },
        sections: {
          where: { isPublished: true },
          orderBy: { createdAt: 'asc' },
          include: {
            chapters: {
              where: { isPublished: true },
              orderBy: { createdAt: 'asc' },
              include: {
                lessons: {
                  where: { isPublished: true },
                  orderBy: { createdAt: 'asc' },
                  select: {
                    id: true,
                    title: true,
                    type: true,
                    duration: true,
                    isFree: true,
                    description: true
                  }
                }
              }
            }
          }
        },

        _count: {
          select: {
            enrollments: true,
            reviews: {
              where: { isPublic: true }
            }
          }
        },
        reviews: {
          where: { isPublic: true },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 5 // Get latest 5 reviews for preview
        }
      }
    })

    // If not found by slug, try to find by ID (for backward compatibility)
    if (!course) {
      course = await prisma.course.findUnique({
        where: {
          id: courseSlug,
          isActive: true,
          isPublished: true
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              _count: {
                select: {
                  instructedCourses: {
                    where: {
                      isActive: true,
                      isPublished: true
                    }
                  },
                  courseEnrollments: true
                }
              }
            }
          },
          sections: {
            where: { isPublished: true },
            orderBy: { createdAt: 'asc' },
            include: {
              chapters: {
                where: { isPublished: true },
                orderBy: { createdAt: 'asc' },
                include: {
                  lessons: {
                    where: { isPublished: true },
                    orderBy: { createdAt: 'asc' },
                    select: {
                      id: true,
                      title: true,
                      type: true,
                      duration: true,
                      isFree: true,
                      description: true
                    }
                  }
                }
              }
            }
          },
          _count: {
            select: {
              enrollments: true,
              reviews: {
                where: { isPublic: true }
              }
            }
          },
          reviews: {
            where: { isPublic: true },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 5 // Get latest 5 reviews for preview
          }
        }
      })
    }

    if (!course) {
      return APIResponse.error('Course not found', 404)
    }

    console.log('Found course:', course.title)
    console.log('Course has sections:', !!course.sections)
    console.log('Course has instructor:', !!course.instructor)

    // Check if user is enrolled (if authenticated)
    let isEnrolled = false
    if (userId) {
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: userId,
            courseId: course.id
          }
        }
      })
      isEnrolled = enrollment?.status === 'active'
    }

    // Calculate course statistics
    let totalLessons = 0
    let totalDuration = 0

    const processedSections = (course as any).sections?.map((section: any) => {
      const lessons = section.chapters?.flatMap((chapter: any) => chapter.lessons || []) || []
      const sectionDuration = lessons.reduce((acc: number, lesson: any) => {
        const duration = parseInt(lesson.duration?.toString() || '0')
        return acc + (isNaN(duration) ? 0 : duration)
      }, 0)

      totalLessons += lessons.length
      totalDuration += sectionDuration

      return {
        id: section.id,
        title: section.title,
        lessonsCount: lessons.length,
        duration: sectionDuration > 0 ? `${Math.round(sectionDuration / 60)} min` : '0 min',
        lessons: lessons.map((lesson: any) => ({
          id: lesson.id,
          title: lesson.title,
          type: lesson.type || 'video',
          duration: lesson.duration ? `${lesson.duration} min` : '0 min',
          isFree: lesson.isFree || false,
          description: lesson.description || ''
        }))
      }
    }) || []

    // Format course data for preview
    const coursePreview = {
      id: course.id,
      title: course.title,
      description: course.description || '',
      price: course.price,
      originalPrice: course.originalPrice,
      slug: course.slug,
      thumbnailImage: course.thumbnailImage,
      category: course.category || 'General',
      level: course.level || 'Beginner',
      duration: totalDuration >= 3600
        ? `${Math.round(totalDuration / 3600)} hours`
        : `${Math.round(totalDuration / 60)} minutes`,
      language: course.language || 'English',
      instructor: {
        id: (course as any).instructor?.id || '',
        name: (course as any).instructor?.name || 'Unknown Instructor',
        image: (course as any).instructor?.image || null,
        bio: (course as any).instructor?.bio || null,
        rating: 4.8, // TODO: Calculate from actual reviews
        studentsCount: (course as any).instructor?._count?.courseEnrollments || 0,
        coursesCount: (course as any).instructor?._count?.instructedCourses || 0
      },
      rating: course.rating || 4.5,
      reviewsCount: (course as any)._count?.reviews || 0,
      studentsCount: (course as any)._count?.enrollments || 0,
      features: course.features || [
        'On-demand video',
        'Downloadable resources',
        'Full lifetime access',
        'Access on mobile and TV',
        'Certificate of completion'
      ],
      requirements: course.requirements || [
        'No prior experience required',
        'A computer with internet access',
        'Willingness to learn'
      ],
      whatYouWillLearn: course.whatYouLearn || [
        'Master the fundamentals',
        'Build practical projects',
        'Gain industry-relevant skills',
        'Prepare for real-world applications'
      ],
      sections: processedSections,
      reviews: (course as any).reviews || [],
      isEnrolled,
      lastUpdated: course.updatedAt.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long'
      })
    }

    return APIResponse.success({
      course: coursePreview
    })

  } catch (error) {
    console.error('Course preview fetch error:', error)
    return APIResponse.error('Failed to fetch course preview', 500)
  }
}
