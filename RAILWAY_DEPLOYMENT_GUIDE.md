# Railway.com Deployment Guide for PrepLocus

This comprehensive guide will help you deploy the PrepLocus course management system to Railway.com, including the Next.js application, PostgreSQL database, Socket.io server, and all required services.

## 📋 Table of Contents

1. [Project Architecture Overview](#project-architecture-overview)
2. [Prerequisites](#prerequisites)
3. [Database Setup](#database-setup)
4. [Environment Variables Configuration](#environment-variables-configuration)
5. [Next.js Application Deployment](#nextjs-application-deployment)
6. [Socket.io Server Deployment](#socketio-server-deployment)
7. [File Storage Configuration](#file-storage-configuration)
8. [Domain and SSL Setup](#domain-and-ssl-setup)
9. [Performance Optimization](#performance-optimization)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)
11. [Troubleshooting](#troubleshooting)

## 🏗️ Project Architecture Overview

The PrepLocus platform consists of several components that need to be deployed:

### Core Components
- **Next.js Application**: Main web application with API routes
- **PostgreSQL Database**: Primary data storage with Prisma ORM
- **Socket.io Server**: Real-time notifications and live features
- **File Storage**: Bunny CDN for videos, images, and documents
- **Payment Gateway**: Razorpay integration for course purchases

### Key Features
- Course management system with video streaming
- Real-time notifications and live quiz sessions
- Payment processing with Razorpay
- User authentication with NextAuth.js
- File uploads and CDN integration
- Email notifications and system alerts

## 🚀 Prerequisites

Before starting the deployment, ensure you have:

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Domain Name** (optional): For custom domain setup
4. **Third-party Service Accounts**:
   - Bunny CDN account for file storage
   - Razorpay account for payments
   - Email service (Gmail/SMTP) for notifications
   - OAuth providers (Google, GitHub) for authentication

## 🗄️ Database Setup

### Step 1: Create PostgreSQL Database

1. **Login to Railway Dashboard**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login to Railway
   railway login
   ```

2. **Create New Project**
   ```bash
   # Create new project
   railway new
   
   # Or link existing project
   railway link
   ```

3. **Add PostgreSQL Service**
   - Go to your Railway project dashboard
   - Click "New Service" → "Database" → "PostgreSQL"
   - Railway will automatically provision a PostgreSQL instance

4. **Get Database Connection Details**
   - Navigate to your PostgreSQL service
   - Go to "Variables" tab
   - Copy the `DATABASE_URL` (it will look like: `postgresql://user:password@host:port/database`)

### Step 2: Configure Prisma for Production

1. **Update Prisma Configuration**
   ```typescript
   // prisma/schema.prisma
   generator client {
     provider = "prisma-client-js"
     output   = "./generated/prisma"
   }
   
   datasource db {
     provider = "postgresql"
     url      = env("DATABASE_URL")
   }
   ```

2. **Create Migration Script**
   ```json
   // package.json - Add these scripts
   {
     "scripts": {
       "build": "next build",
       "start": "next start",
       "postinstall": "prisma generate",
       "db:migrate": "prisma migrate deploy",
       "db:seed": "tsx scripts/seed.ts"
     }
   }
   ```

## 🔧 Environment Variables Configuration

### Step 1: Prepare Environment Variables

Create a comprehensive list of all required environment variables:

```bash
# Database
DATABASE_URL="postgresql://user:password@host:port/database"

# NextAuth.js Configuration
NEXTAUTH_URL="https://your-app.railway.app"
NEXTAUTH_SECRET="your-super-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Razorpay Payment Gateway
RAZORPAY_KEY_ID="your-razorpay-key-id"
RAZORPAY_KEY_SECRET="your-razorpay-key-secret"
NEXT_PUBLIC_RAZORPAY_KEY_ID="your-razorpay-key-id"
RAZORPAY_WEBHOOK_SECRET="your-razorpay-webhook-secret"

# Bunny CDN Configuration
BUNNY_STORAGE_ZONE_NAME="your-storage-zone-name"
BUNNY_STORAGE_ACCESS_KEY="your-bunny-access-key"
BUNNY_PULL_ZONE_URL="https://your-pull-zone.b-cdn.net"
BUNNY_STORAGE_REGION="storage"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"
CONTACT_EMAIL="<EMAIL>"

# Socket Server Configuration
SOCKET_PORT=3001
NEXT_PUBLIC_SOCKET_URL="https://your-socket-server.railway.app"
NEXT_PUBLIC_SOCKET_ENABLED=true

# API Configuration
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100

# Production Settings
NODE_ENV=production
```

### Step 2: Set Environment Variables in Railway

1. **For Next.js Application**:
   - Go to your Next.js service in Railway
   - Navigate to "Variables" tab
   - Add each environment variable one by one
   - **Important**: Don't include quotes around values in Railway

2. **For Socket Server**:
   - Create separate service for Socket server
   - Add relevant environment variables
   - Ensure `SOCKET_PORT` is set to Railway's provided port

## 🚀 Next.js Application Deployment

### Step 1: Prepare Application for Deployment

1. **Create Railway Configuration**
   ```json
   // railway.json
   {
     "$schema": "https://railway.app/railway.schema.json",
     "build": {
       "builder": "NIXPACKS"
     },
     "deploy": {
       "numReplicas": 1,
       "sleepApplication": false,
       "restartPolicyType": "ON_FAILURE"
     }
   }
   ```

2. **Update Package.json Build Scripts**
   ```json
   {
     "scripts": {
       "build": "prisma generate && next build",
       "start": "next start",
       "postinstall": "prisma generate",
       "railway:build": "npm run build",
       "railway:start": "npm run start"
     }
   }
   ```

3. **Create Dockerfile (Optional but Recommended)**
   ```dockerfile
   FROM node:18-alpine AS base
   
   # Install dependencies only when needed
   FROM base AS deps
   RUN apk add --no-cache libc6-compat
   WORKDIR /app
   
   COPY package.json package-lock.json* ./
   RUN npm ci --only=production
   
   # Rebuild the source code only when needed
   FROM base AS builder
   WORKDIR /app
   COPY --from=deps /app/node_modules ./node_modules
   COPY . .
   
   # Generate Prisma client
   RUN npx prisma generate
   
   # Build application
   RUN npm run build
   
   # Production image
   FROM base AS runner
   WORKDIR /app
   
   ENV NODE_ENV production
   
   RUN addgroup --system --gid 1001 nodejs
   RUN adduser --system --uid 1001 nextjs
   
   COPY --from=builder /app/public ./public
   COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
   COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
   
   USER nextjs
   
   EXPOSE 3000
   
   ENV PORT 3000
   
   CMD ["node", "server.js"]
   ```

### Step 2: Deploy to Railway

1. **Connect GitHub Repository**
   - In Railway dashboard, click "New Service"
   - Select "GitHub Repo"
   - Choose your repository
   - Railway will automatically detect it's a Next.js app

2. **Configure Build Settings**
   - Build Command: `npm run build`
   - Start Command: `npm run start`
   - Install Command: `npm ci`

3. **Run Database Migrations**
   ```bash
   # After first deployment, run migrations
   railway run npx prisma migrate deploy
   
   # Optional: Seed database
   railway run npm run db:seed
   ```

## 🔌 Socket.io Server Deployment

### Step 1: Create Separate Socket Service

The Socket.io server needs to be deployed as a separate service for better scalability.

1. **Create Socket Server Configuration**
   ```javascript
   // server/railway-socket-server.js
   const { createServer } = require('http')
   const { Server } = require('socket.io')
   const { PrismaClient } = require('../lib/generated/prisma')
   
   const prisma = new PrismaClient()
   const port = process.env.PORT || 3001
   
   const httpServer = createServer()
   const io = new Server(httpServer, {
     cors: {
       origin: process.env.NEXTAUTH_URL || "*",
       methods: ["GET", "POST"],
       credentials: true
     },
     path: '/socket.io',
     transports: ['websocket', 'polling']
   })
   
   // Socket connection handling
   io.on('connection', (socket) => {
     console.log('🔌 New socket connection:', socket.id)
     
     // Add your socket event handlers here
     // ... (copy from existing socket-server.js)
   })
   
   httpServer.listen(port, () => {
     console.log(`🚀 Socket server running on port ${port}`)
   })
   ```

2. **Create Package.json for Socket Service**
   ```json
   {
     "name": "preplocus-socket-server",
     "version": "1.0.0",
     "scripts": {
       "start": "node server/railway-socket-server.js",
       "postinstall": "prisma generate"
     },
     "dependencies": {
       "socket.io": "^4.7.2",
       "@prisma/client": "^6.12.0"
     }
   }
   ```

### Step 2: Deploy Socket Server

1. **Create New Service in Railway**
   - Add new service to your project
   - Connect same GitHub repository
   - Set custom start command: `npm run start:socket`

2. **Configure Environment Variables**
   - Add all necessary environment variables
   - Ensure `PORT` is set correctly (Railway provides this automatically)
   - Update `NEXT_PUBLIC_SOCKET_URL` in main app to point to socket service URL

## 📁 File Storage Configuration

### Step 1: Configure Bunny CDN

1. **Create Bunny CDN Account**
   - Sign up at [bunny.net](https://bunny.net)
   - Create a storage zone
   - Create a pull zone linked to your storage zone

2. **Configure Storage Structure**
   ```
   your-storage-zone/
   ├── courses/
   │   ├── {courseId}/
   │   │   ├── images/
   │   │   ├── videos/
   │   │   └── files/
   ├── users/
   │   └── avatars/
   └── system/
       └── assets/
   ```

3. **Update Environment Variables**
   ```bash
   BUNNY_STORAGE_ZONE_NAME="your-storage-zone"
   BUNNY_STORAGE_ACCESS_KEY="your-access-key"
   BUNNY_PULL_ZONE_URL="https://your-pull-zone.b-cdn.net"
   BUNNY_STORAGE_REGION="storage"  # or your preferred region
   ```

### Step 2: Configure File Upload Limits

1. **Update Next.js Configuration**
   ```javascript
   // next.config.js
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     experimental: {
       serverComponentsExternalPackages: ['@prisma/client']
     },
     images: {
       domains: ['your-pull-zone.b-cdn.net'],
       remotePatterns: [
         {
           protocol: 'https',
           hostname: 'your-pull-zone.b-cdn.net',
           port: '',
           pathname: '/**',
         },
       ],
     },
     // Increase file upload limits for Railway
     serverRuntimeConfig: {
       maxFileSize: '100mb'
     }
   }
   
   module.exports = nextConfig
   ```

## 🌐 Domain and SSL Setup

### Step 1: Configure Custom Domain (Optional)

1. **Add Domain in Railway**
   - Go to your Next.js service
   - Navigate to "Settings" → "Domains"
   - Click "Custom Domain"
   - Enter your domain name

2. **Configure DNS Records**
   ```
   Type: CNAME
   Name: @ (or subdomain)
   Value: your-app.railway.app
   ```

3. **Update Environment Variables**
   ```bash
   NEXTAUTH_URL="https://yourdomain.com"
   NEXT_PUBLIC_SOCKET_URL="https://socket.yourdomain.com"
   ```

### Step 2: SSL Configuration

Railway automatically provides SSL certificates for both Railway subdomains and custom domains. No additional configuration is needed.

## ⚡ Performance Optimization

### Step 1: Database Optimization

1. **Connection Pooling**
   ```typescript
   // lib/prisma.ts
   import { PrismaClient } from './generated/prisma'
   
   const globalForPrisma = globalThis as unknown as {
     prisma: PrismaClient | undefined
   }
   
   export const prisma = globalForPrisma.prisma ?? new PrismaClient({
     log: ['query'],
     datasources: {
       db: {
         url: process.env.DATABASE_URL + '?connection_limit=5&pool_timeout=20'
       }
     }
   })
   
   if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
   ```

2. **Database Indexing**
   ```sql
   -- Add these indexes for better performance
   CREATE INDEX idx_course_enrollments_user_id ON course_enrollments(user_id);
   CREATE INDEX idx_course_enrollments_course_id ON course_enrollments(course_id);
   CREATE INDEX idx_payments_user_id ON payments(user_id);
   CREATE INDEX idx_payments_status ON payments(status);
   CREATE INDEX idx_courses_published ON courses(is_published, is_active);
   ```

### Step 2: Application Optimization

1. **Enable Next.js Optimizations**
   ```javascript
   // next.config.js
   const nextConfig = {
     // Enable compression
     compress: true,
     
     // Enable image optimization
     images: {
       formats: ['image/webp', 'image/avif'],
       minimumCacheTTL: 60,
     },
     
     // Enable experimental features
     experimental: {
       optimizeCss: true,
       optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
     }
   }
   ```

2. **Configure Caching**
   ```typescript
   // lib/cache.ts
   import { unstable_cache } from 'next/cache'
   
   export const getCachedCourses = unstable_cache(
     async () => {
       return await prisma.course.findMany({
         where: { isPublished: true, isActive: true },
         include: { instructor: true }
       })
     },
     ['courses'],
     { revalidate: 300 } // 5 minutes
   )
   ```

## 📊 Monitoring and Maintenance

### Step 1: Set Up Monitoring

1. **Railway Metrics**
   - Monitor CPU, Memory, and Network usage in Railway dashboard
   - Set up alerts for high resource usage
   - Monitor deployment logs for errors

2. **Application Monitoring**
   ```typescript
   // lib/monitoring.ts
   export const logError = (error: Error, context: string) => {
     console.error(`[${context}] ${error.message}`, {
       stack: error.stack,
       timestamp: new Date().toISOString()
     })
   }
   
   export const logPerformance = (operation: string, duration: number) => {
     if (duration > 1000) {
       console.warn(`Slow operation: ${operation} took ${duration}ms`)
     }
   }
   ```

### Step 2: Backup Strategy

1. **Database Backups**
   ```bash
   # Create backup script
   #!/bin/bash
   DATE=$(date +%Y%m%d_%H%M%S)
   railway run pg_dump $DATABASE_URL > backup_$DATE.sql
   ```

2. **File Storage Backups**
   - Configure Bunny CDN replication
   - Set up automated backups for critical files
   - Document recovery procedures

## 🔧 Troubleshooting

### Common Issues and Solutions

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   railway run npx prisma db pull
   
   # Reset database if needed
   railway run npx prisma migrate reset
   ```

2. **Socket Connection Issues**
   ```javascript
   // Debug socket connections
   io.on('connection', (socket) => {
     console.log('Connection from:', socket.handshake.headers.origin)
     
     socket.on('disconnect', (reason) => {
       console.log('Disconnect reason:', reason)
     })
   })
   ```

3. **File Upload Issues**
   ```typescript
   // Check Bunny CDN configuration
   const testUpload = async () => {
     try {
       const response = await fetch(`https://${BUNNY_STORAGE_ZONE}.b-cdn.net/test.txt`, {
         method: 'PUT',
         headers: {
           'AccessKey': BUNNY_ACCESS_KEY,
           'Content-Type': 'text/plain'
         },
         body: 'Test file'
       })
       console.log('Upload test:', response.status)
     } catch (error) {
       console.error('Upload test failed:', error)
     }
   }
   ```

4. **Payment Integration Issues**
   ```typescript
   // Test Razorpay configuration
   const testRazorpay = async () => {
     try {
       const order = await razorpay.orders.create({
         amount: 100, // ₹1 in paise
         currency: 'INR',
         receipt: 'test_receipt'
       })
       console.log('Razorpay test successful:', order.id)
     } catch (error) {
       console.error('Razorpay test failed:', error)
     }
   }
   ```

### Performance Issues

1. **Slow Database Queries**
   ```typescript
   // Enable query logging
   const prisma = new PrismaClient({
     log: [
       { emit: 'event', level: 'query' },
       { emit: 'stdout', level: 'error' },
       { emit: 'stdout', level: 'info' },
       { emit: 'stdout', level: 'warn' },
     ],
   })
   
   prisma.$on('query', (e) => {
     if (e.duration > 1000) {
       console.log('Slow query:', e.query, `Duration: ${e.duration}ms`)
     }
   })
   ```

2. **Memory Issues**
   ```javascript
   // Monitor memory usage
   setInterval(() => {
     const used = process.memoryUsage()
     console.log('Memory usage:', {
       rss: Math.round(used.rss / 1024 / 1024) + 'MB',
       heapTotal: Math.round(used.heapTotal / 1024 / 1024) + 'MB',
       heapUsed: Math.round(used.heapUsed / 1024 / 1024) + 'MB'
     })
   }, 30000) // Every 30 seconds
   ```

## 🎯 Post-Deployment Checklist

- [ ] Database migrations completed successfully
- [ ] All environment variables configured
- [ ] OAuth providers working (Google, GitHub)
- [ ] Razorpay payment flow tested
- [ ] File uploads to Bunny CDN working
- [ ] Email notifications sending
- [ ] Socket.io real-time features working
- [ ] SSL certificate active
- [ ] Custom domain configured (if applicable)
- [ ] Monitoring and alerts set up
- [ ] Backup strategy implemented
- [ ] Performance optimizations applied

## 📞 Support and Resources

- **Railway Documentation**: [docs.railway.app](https://docs.railway.app)
- **Next.js Deployment**: [nextjs.org/docs/deployment](https://nextjs.org/docs/deployment)
- **Prisma Railway Guide**: [prisma.io/docs/guides/deployment/deployment-guides/deploying-to-railway](https://www.prisma.io/docs/guides/deployment/deployment-guides/deploying-to-railway)
- **Socket.io Documentation**: [socket.io/docs](https://socket.io/docs)

---

This deployment guide provides a comprehensive approach to deploying the PrepLocus platform on Railway.com. Follow each section carefully and test thoroughly before going live with your production environment.
