import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { quizBundleService } from '@/lib/quiz-bundle-service'
import { prisma } from '@/lib/prisma'

// GET /api/student/quiz-bundles/[id]/access - Check bundle access and get details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const bundleId = params?.id as string

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      // Check if user has purchased this bundle
      const purchase = await prisma.quizBundlePurchase.findUnique({
        where: {
          userId_bundleId: {
            userId: user.id,
            bundleId: bundleId
          }
        },
        include: {
          bundle: {
            include: {
              items: {
                include: {
                  quiz: {
                    select: {
                      id: true,
                      title: true,
                      description: true,
                      type: true,
                      difficulty: true,
                      timeLimit: true,
                      estimatedTime: true,
                      points: true,
                      category: true,
                      tags: true,
                      isPublished: true
                    }
                  }
                },
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!purchase || purchase.status !== 'active') {
        return APIResponse.error('Bundle access denied', 403)
      }

      // Check if bundle is expired
      if (purchase.expiresAt && purchase.expiresAt < new Date()) {
        return APIResponse.error('Bundle access has expired', 403)
      }

      // Calculate progress
      const progress = await quizBundleService.calculateBundleProgress(
        user.id,
        bundleId
      )

      // Get quiz attempt history for this bundle
      const quizIds = purchase.bundle.items.map(item => item.quiz.id)
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          quizId: { in: quizIds }
        },
        select: {
          id: true,
          quizId: true,
          score: true,
          isCompleted: true,
          startedAt: true,
          completedAt: true,
          timeSpent: true
        },
        orderBy: { startedAt: 'desc' }
      })

      // Group attempts by quiz
      const attemptsByQuiz = attempts.reduce((acc, attempt) => {
        if (!acc[attempt.quizId]) {
          acc[attempt.quizId] = []
        }
        acc[attempt.quizId].push(attempt)
        return acc
      }, {} as Record<string, typeof attempts>)

      // Update last accessed time
      await prisma.quizBundlePurchase.update({
        where: { id: purchase.id },
        data: { lastAccessedAt: new Date() }
      })

      // Format response
      const bundleData = {
        id: purchase.bundle.id,
        title: purchase.bundle.title,
        description: purchase.bundle.description,
        shortDescription: purchase.bundle.shortDescription,
        slug: purchase.bundle.slug,
        thumbnailImage: purchase.bundle.thumbnailImage,
        category: purchase.bundle.category,
        level: purchase.bundle.level,
        duration: purchase.bundle.duration,
        tags: purchase.bundle.tags,
        features: purchase.bundle.features,
        price: purchase.bundle.price,
        purchaseInfo: {
          id: purchase.id,
          status: purchase.status,
          purchasedAt: purchase.purchasedAt,
          expiresAt: purchase.expiresAt,
          progress: purchase.progress,
          lastAccessedAt: purchase.lastAccessedAt
        },
        progress: {
          totalQuizzes: progress.totalQuizzes,
          completedQuizzes: progress.completedQuizzes,
          progressPercentage: progress.progressPercentage,
          lastAccessedAt: progress.lastAccessedAt
        },
        quizzes: purchase.bundle.items.map(item => {
          const quizAttempts = attemptsByQuiz[item.quiz.id] || []
          const bestAttempt = quizAttempts
            .filter(a => a.isCompleted)
            .sort((a, b) => (b.score || 0) - (a.score || 0))[0]
          const latestAttempt = quizAttempts[0]

          return {
            id: item.quiz.id,
            title: item.quiz.title,
            description: item.quiz.description,
            type: item.quiz.type,
            difficulty: item.quiz.difficulty,
            timeLimit: item.quiz.timeLimit,
            estimatedTime: item.quiz.estimatedTime,
            points: item.quiz.points,
            category: item.quiz.category,
            tags: item.quiz.tags,
            order: item.order,
            isRequired: item.isRequired,
            isPublished: item.quiz.isPublished,
            attempts: {
              total: quizAttempts.length,
              completed: quizAttempts.filter(a => a.isCompleted).length,
              bestScore: bestAttempt?.score || null,
              latestScore: latestAttempt?.score || null,
              lastAttemptAt: latestAttempt?.startedAt || null,
              isCompleted: !!bestAttempt
            }
          }
        })
      }

      return APIResponse.success({
        bundle: bundleData,
        hasAccess: true,
        accessType: 'purchased'
      })

    } catch (error) {
      console.error('Error checking bundle access:', error)
      return APIResponse.error('Failed to check bundle access', 500)
    }
  }
)
