import { NextRequest } from 'next/server'
 import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const startAttemptSchema = z.object({
  resumeAttemptId: z.string().optional()
})

// POST /api/student/quizzes/[id]/attempt - Start a new quiz attempt or resume existing
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
    validateBody: startAttemptSchema
  },
  async (request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const { resumeAttemptId } = validatedBody

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    try {
      console.log(`Starting quiz attempt for quiz ${quizId} by user ${user.id}`)

      // If resumeAttemptId is provided, try to resume that specific attempt
      if (resumeAttemptId) {
        const existingAttempt = await prisma.quizAttempt.findUnique({
          where: {
            id: resumeAttemptId,
            userId: user.id,
            quizId,
            isCompleted: false
          }
        })

        if (existingAttempt) {
          console.log(`Resuming specific attempt: ${existingAttempt.id}`)

          // Get quiz questions for the resumed attempt
          const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  difficulty: true,
                  tags: true,
                  image: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          })

          if (!quiz) {
            return APIResponse.error('Quiz not found', 404)
          }

          return APIResponse.success({
            attemptId: existingAttempt.id,
            quizId: existingAttempt.quizId,
            questions: quiz.questions.map(q => ({
              id: q.id,
              type: q.type,
              text: q.text,
              options: q.options,
              correctAnswer: q.correctAnswer,
              explanation: q.explanation,
              points: q.points,
              difficulty: q.difficulty,
              tags: q.tags,
              imageUrl: q.image,
              order: q.order
            })),
            timeLimit: quiz.timeLimit,
            startedAt: existingAttempt.startedAt.toISOString(),
            answers: existingAttempt.answers || {},
            isCompleted: false,
            isPaused: false // Resume means we're no longer paused
          }, 'Resuming quiz attempt')
        }
      }

      // Check if quiz exists and is published
      const quiz = await prisma.quiz.findUnique({
        where: { id: quizId },
        include: {
          questions: {
            select: {
              id: true,
              type: true,
              text: true,
              options: true,
              correctAnswer: true,
              explanation: true,
              points: true,
              difficulty: true,
              tags: true,
              image: true,
              order: true
            },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      if (!quiz.isPublished) {
        return APIResponse.error('Quiz is not available', 400)
      }

      // Check if quiz is within time bounds
      const now = new Date()
      if (quiz.startTime && quiz.startTime > now) {
        return APIResponse.error('Quiz has not started yet', 400)
      }

      if (quiz.endTime && quiz.endTime < now) {
        return APIResponse.error('Quiz has ended', 400)
      }

      // Check if user has access to this quiz through bundle purchase
      const { quizBundleService } = await import('@/lib/quiz-bundle-service')
      const hasAccess = await quizBundleService.hasQuizAccess(user.id, quizId)

      if (!hasAccess) {
        return APIResponse.error('Access denied. This quiz requires a bundle purchase.', 403)
      }

      // Check if user is enrolled
      const enrollment = await prisma.quizEnrollment.findFirst({
        where: {
          quizId,
          userId: user.id
        }
      })

      if (!enrollment) {
        return APIResponse.error('You must enroll in this quiz first', 400)
      }

      // Check if user has exceeded max attempts
      const existingAttempts = await prisma.quizAttempt.findMany({
        where: {
          quizId,
          userId: user.id
        },
        orderBy: { startedAt: 'desc' }
      })

      if (quiz.maxAttempts > 0 && existingAttempts.length >= quiz.maxAttempts) {
        return APIResponse.error(
          `You have reached the maximum number of attempts (${quiz.maxAttempts}) for this quiz. No more attempts are allowed.`,
          400,
          'MAX_ATTEMPTS_EXCEEDED'
        )
      }

      // Check if there's an active (incomplete) attempt
      const activeAttempt = existingAttempts.find(attempt => !attempt.isCompleted)
      
      if (activeAttempt) {
        console.log(`Resuming existing attempt: ${activeAttempt.id}`)
        
        // Return the existing attempt
        return APIResponse.success({
          attemptId: activeAttempt.id,
          quizId: activeAttempt.quizId,
          questions: quiz.questions.map(q => ({
            id: q.id,
            type: q.type,
            text: q.text,
            options: q.options,
            correctAnswer: q.correctAnswer,
            explanation: q.explanation,
            points: q.points,
            difficulty: q.difficulty,
            tags: q.tags,
            imageUrl: q.image,
            order: q.order
          })),
          timeLimit: quiz.timeLimit,
          startedAt: activeAttempt.startedAt.toISOString(),
          answers: activeAttempt.answers || {},
          isCompleted: false,
          isPaused: activeAttempt.isPaused || false
        }, 'Resuming existing quiz attempt')
      }

      // Create new attempt
      console.log(`Creating new quiz attempt for quiz ${quizId}`)
      
      const newAttempt = await prisma.quizAttempt.create({
        data: {
          quizId,
          userId: user.id,
          startedAt: new Date(),
          answers: {},
          score: 0,
          totalPoints: quiz.questions.reduce((sum, q) => sum + q.points, 0),
          percentage: 0,
          correctAnswers: 0,
          incorrectAnswers: 0,
          unansweredQuestions: quiz.questions.length,
          isCompleted: false,
          isPaused: false
        }
      })

      console.log(`Quiz attempt created successfully: ${newAttempt.id}`)

      // Return the new attempt with quiz questions
      return APIResponse.success({
        attemptId: newAttempt.id,
        quizId: newAttempt.quizId,
        questions: quiz.questions.map(q => ({
          id: q.id,
          type: q.type,
          text: q.text,
          options: q.options,
          correctAnswer: q.correctAnswer,
          explanation: q.explanation,
          points: q.points,
          difficulty: q.difficulty,
          tags: q.tags,
          imageUrl: q.image,
          order: q.order
        })),
        timeLimit: quiz.timeLimit,
        startedAt: newAttempt.startedAt.toISOString(),
        answers: {},
        isCompleted: false,
        isPaused: false
      }, 'Quiz attempt started successfully')

    } catch (error) {
      console.error('Error starting quiz attempt:', error)
      console.error('Error details:', error instanceof Error ? error.message : String(error))
      
      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to start quiz attempt',
        500
      )
    }
  }
)

const pauseResumeSchema = z.object({
  isPaused: z.boolean(),
  attemptId: z.string()
})

// PATCH /api/student/quizzes/[id]/attempt - Pause or resume a quiz attempt
export const PATCH = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
    validateBody: pauseResumeSchema
  },
  async (_request: NextRequest, { user, params, validatedBody }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const { isPaused, attemptId } = validatedBody

    if (!quizId || !attemptId) {
      return APIResponse.error('Quiz ID and Attempt ID are required', 400)
    }

    try {
      console.log(`${isPaused ? 'Pausing' : 'Resuming'} quiz attempt ${attemptId} by user ${user.id}`)

      // Verify the attempt belongs to the user and is active
      const attempt = await prisma.quizAttempt.findUnique({
        where: {
          id: attemptId,
          userId: user.id,
          quizId,
          isCompleted: false
        }
      })

      if (!attempt) {
        return APIResponse.error('Quiz attempt not found or already completed', 404)
      }

      // Update the pause status
      const updatedAttempt = await prisma.quizAttempt.update({
        where: { id: attemptId },
        data: {
          isPaused
        }
      })

      console.log(`Quiz attempt ${isPaused ? 'paused' : 'resumed'} successfully: ${attemptId}`)

      return APIResponse.success({
        attemptId: updatedAttempt.id,
        isPaused: updatedAttempt.isPaused,
        updatedAt: new Date().toISOString()
      }, `Quiz attempt ${isPaused ? 'paused' : 'resumed'} successfully`)

    } catch (error) {
      console.error(`Error ${isPaused ? 'pausing' : 'resuming'} quiz attempt:`, error)
      console.error('Error details:', error instanceof Error ? error.message : String(error))

      return APIResponse.error(
        error instanceof Error ? error.message : `Failed to ${isPaused ? 'pause' : 'resume'} quiz attempt`,
        500
      )
    }
  }
)

// GET /api/student/quizzes/[id]/attempt - Get current attempt status
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (_request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    try {
      console.log(`Getting current attempt status for quiz ${quizId} by user ${user.id}`)

      // Find the most recent incomplete attempt for this user and quiz
      const attempt = await prisma.quizAttempt.findFirst({
        where: {
          quizId,
          userId: user.id,
          isCompleted: false
        },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  difficulty: true,
                  tags: true,
                  image: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          }
        },
        orderBy: { startedAt: 'desc' }
      })

      if (!attempt) {
        return APIResponse.error('No active attempt found', 404)
      }

      return APIResponse.success({
        attemptId: attempt.id,
        quizId: attempt.quizId,
        questions: attempt.quiz.questions,
        timeLimit: attempt.quiz.timeLimit,
        startedAt: attempt.startedAt.toISOString(),
        completedAt: attempt.completedAt?.toISOString() || null,
        answers: attempt.answers || {},
        isCompleted: attempt.isCompleted,
        isPaused: attempt.isPaused,
        score: attempt.score,
        percentage: attempt.percentage
      }, 'Current attempt status retrieved')

    } catch (error) {
      console.error('Error fetching current attempt status:', error)
      return APIResponse.error('Failed to fetch attempt status', 500)
    }
  }
)
