/**
 * API Endpoint Testing Script
 * Tests the roadmap API endpoints to ensure they're working
 */

const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testAPIEndpoints() {
  console.log('🌐 Testing Roadmap API Endpoints...\n')
  
  const tests = [
    {
      name: 'Admin Roadmap Config',
      url: '/api/admin/courses/test-course-id/roadmap',
      method: 'GET',
      expectedStatus: [401, 403] // Should require auth
    },
    {
      name: 'Student Roadmap Access',
      url: '/api/student/courses/test-course-slug/roadmap',
      method: 'GET',
      expectedStatus: [401, 403] // Should require auth
    },
    {
      name: 'Mission Achievements',
      url: '/api/student/mission-achievements',
      method: 'GET',
      expectedStatus: [401, 403] // Should require auth
    },
    {
      name: 'User Notifications',
      url: '/api/student/notifications',
      method: 'GET',
      expectedStatus: [401, 403] // Should require auth
    },
    {
      name: 'Course Content for Missions',
      url: '/api/admin/courses/test-course-id/content',
      method: 'GET',
      expectedStatus: [401, 403] // Should require auth
    }
  ]
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`)
      
      const response = await fetch(`${BASE_URL}${test.url}`, {
        method: test.method,
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const status = response.status
      const isExpected = test.expectedStatus.includes(status)
      
      if (isExpected) {
        console.log(`   ✅ ${test.name} - Status: ${status} (Expected: ${test.expectedStatus.join(' or ')})`)
        passed++
      } else if (status === 404) {
        console.log(`   ❌ ${test.name} - Status: 404 (Route not found)`)
        failed++
      } else {
        console.log(`   ⚠️  ${test.name} - Status: ${status} (Expected: ${test.expectedStatus.join(' or ')})`)
        // This might be OK - could be a different auth setup
        passed++
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`   ❌ ${test.name} - Server not running`)
        failed++
      } else {
        console.log(`   ❌ ${test.name} - Error: ${error.message}`)
        failed++
      }
    }
  }
  
  console.log('\n' + '='.repeat(50))
  console.log('📊 API Endpoint Test Results')
  console.log('='.repeat(50))
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`)
  
  if (failed === 0) {
    console.log('\n🎉 All API endpoints are properly configured!')
  } else {
    console.log('\n⚠️  Some endpoints may need attention.')
  }
}

// Test server connectivity first
async function testServerConnectivity() {
  console.log('🔌 Testing server connectivity...')
  
  try {
    const response = await fetch(`${BASE_URL}/`, {
      method: 'GET',
      timeout: 5000
    })
    
    if (response.status === 200) {
      console.log('   ✅ Server is running and accessible\n')
      return true
    } else {
      console.log(`   ⚠️  Server responded with status: ${response.status}\n`)
      return true // Still accessible
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('   ❌ Server is not running. Please start with "npm run dev"\n')
      return false
    } else {
      console.log(`   ❌ Connection error: ${error.message}\n`)
      return false
    }
  }
}

async function runTests() {
  const serverRunning = await testServerConnectivity()
  
  if (serverRunning) {
    await testAPIEndpoints()
  } else {
    console.log('🚫 Cannot test API endpoints - server is not accessible')
    console.log('💡 Please run "npm run dev" in another terminal and try again')
  }
}

if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = { testAPIEndpoints, testServerConnectivity }
