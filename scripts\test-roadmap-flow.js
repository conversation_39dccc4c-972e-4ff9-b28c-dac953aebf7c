#!/usr/bin/env node

/**
 * Roadmap Feature Flow Test
 * 
 * This script tests the complete admin-to-student flow of the roadmap feature
 */

const fetch = require('node-fetch')

const TEST_CONFIG = {
  baseUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  adminToken: process.env.TEST_ADMIN_TOKEN,
  studentToken: process.env.TEST_STUDENT_TOKEN,
  testCourseId: 'test-course-roadmap-flow',
  testCourseSlug: 'test-roadmap-course'
}

async function testRoadmapFlow() {
  console.log('🚀 Testing Roadmap Feature Flow...\n')
  
  const results = {
    passed: 0,
    failed: 0,
    errors: []
  }

  try {
    // Test 1: Admin enables roadmap
    await runTest('Admin Enable Roadmap', testAdminEnableRoadmap, results)
    
    // Test 2: Admin creates missions
    await runTest('Admin Create Missions', testAdminCreateMissions, results)
    
    // Test 3: Student accesses roadmap
    await runTest('Student Access Roadmap', testStudentAccessRoadmap, results)
    
    // Test 4: Student starts mission
    await runTest('Student Start Mission', testStudentStartMission, results)
    
    // Test 5: Data consistency check
    await runTest('Data Consistency', testDataConsistency, results)
    
    // Summary
    console.log('\n' + '='.repeat(50))
    console.log('📊 FLOW TEST SUMMARY')
    console.log('='.repeat(50))
    console.log(`✅ Passed: ${results.passed}`)
    console.log(`❌ Failed: ${results.failed}`)
    
    if (results.errors.length > 0) {
      console.log('\n🐛 ERRORS:')
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`)
      })
    }
    
  } catch (error) {
    console.error('💥 Flow test failed:', error)
  }
}

async function runTest(name, testFunction, results) {
  console.log(`🧪 ${name}...`)
  try {
    await testFunction()
    console.log(`   ✅ ${name} - PASSED\n`)
    results.passed++
  } catch (error) {
    console.log(`   ❌ ${name} - FAILED: ${error.message}\n`)
    results.failed++
    results.errors.push(`${name}: ${error.message}`)
  }
}

async function testAdminEnableRoadmap() {
  const roadmapConfig = {
    hasRoadmap: true,
    roadmapTitle: 'Test Learning Journey',
    roadmapDescription: 'A comprehensive test roadmap for validation',
    estimatedCompletion: '2 hours',
    missions: []
  }

  const response = await fetch(`${TEST_CONFIG.baseUrl}/api/admin/courses/${TEST_CONFIG.testCourseId}/roadmap`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TEST_CONFIG.adminToken}`
    },
    body: JSON.stringify(roadmapConfig)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(`Admin API failed: ${error.message || response.statusText}`)
  }

  console.log('     ✓ Roadmap enabled successfully')
}

async function testAdminCreateMissions() {
  const roadmapConfig = {
    hasRoadmap: true,
    roadmapTitle: 'Test Learning Journey',
    roadmapDescription: 'A comprehensive test roadmap for validation',
    estimatedCompletion: '2 hours',
    missions: [
      {
        id: 'mission-1',
        title: 'Getting Started',
        description: 'Introduction to the course',
        icon: '🚀',
        color: '#3B82F6',
        order: 1,
        isRequired: true,
        pointsReward: 100,
        estimatedTime: '30 minutes',
        contents: [],
        prerequisites: []
      },
      {
        id: 'mission-2',
        title: 'Advanced Topics',
        description: 'Deep dive into advanced concepts',
        icon: '🎯',
        color: '#10B981',
        order: 2,
        isRequired: true,
        pointsReward: 200,
        estimatedTime: '45 minutes',
        contents: [],
        prerequisites: ['mission-1']
      }
    ]
  }

  const response = await fetch(`${TEST_CONFIG.baseUrl}/api/admin/courses/${TEST_CONFIG.testCourseId}/roadmap`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TEST_CONFIG.adminToken}`
    },
    body: JSON.stringify(roadmapConfig)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(`Mission creation failed: ${error.message || response.statusText}`)
  }

  console.log('     ✓ Missions created successfully')
}

async function testStudentAccessRoadmap() {
  const response = await fetch(`${TEST_CONFIG.baseUrl}/api/student/courses/${TEST_CONFIG.testCourseSlug}/roadmap`, {
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.studentToken}`
    }
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(`Student roadmap access failed: ${error.message || response.statusText}`)
  }

  const data = await response.json()
  
  if (!data.data.course.hasRoadmap) {
    throw new Error('Roadmap not enabled for student')
  }

  if (!data.data.missions || data.data.missions.length === 0) {
    throw new Error('No missions found for student')
  }

  console.log(`     ✓ Student can access roadmap with ${data.data.missions.length} missions`)
}

async function testStudentStartMission() {
  // First get the missions to find the first one
  const roadmapResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/student/courses/${TEST_CONFIG.testCourseSlug}/roadmap`, {
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.studentToken}`
    }
  })

  if (!roadmapResponse.ok) {
    throw new Error('Failed to get missions for start test')
  }

  const roadmapData = await roadmapResponse.json()
  const firstMission = roadmapData.data.missions[0]

  if (!firstMission) {
    throw new Error('No missions available to start')
  }

  // Start the mission
  const startResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/student/courses/${TEST_CONFIG.testCourseSlug}/roadmap/missions/${firstMission.id}/start`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.studentToken}`
    }
  })

  if (!startResponse.ok) {
    const error = await startResponse.json()
    throw new Error(`Mission start failed: ${error.message || startResponse.statusText}`)
  }

  const startData = await startResponse.json()
  
  if (!startData.data.missionProgress.isStarted) {
    throw new Error('Mission not marked as started')
  }

  console.log('     ✓ Student can start missions successfully')
}

async function testDataConsistency() {
  // Get admin view
  const adminResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/admin/courses/${TEST_CONFIG.testCourseId}/roadmap`, {
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.adminToken}`
    }
  })

  // Get student view
  const studentResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/student/courses/${TEST_CONFIG.testCourseSlug}/roadmap`, {
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.studentToken}`
    }
  })

  if (!adminResponse.ok || !studentResponse.ok) {
    throw new Error('Failed to fetch data for consistency check')
  }

  const adminData = await adminResponse.json()
  const studentData = await studentResponse.json()

  // Check mission count consistency
  if (adminData.data.missions.length !== studentData.data.missions.length) {
    throw new Error('Mission count mismatch between admin and student views')
  }

  // Check roadmap settings consistency
  if (adminData.data.hasRoadmap !== studentData.data.course.hasRoadmap) {
    throw new Error('Roadmap enabled status mismatch')
  }

  console.log('     ✓ Data consistency verified between admin and student views')
}

// Run tests if script is executed directly
if (require.main === module) {
  testRoadmapFlow()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Flow test failed:', error)
      process.exit(1)
    })
}

module.exports = { testRoadmapFlow, TEST_CONFIG }
