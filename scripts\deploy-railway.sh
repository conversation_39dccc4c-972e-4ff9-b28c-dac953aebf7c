#!/bin/bash

# Railway Deployment Script for PrepLocus
# This script helps automate the deployment process to Railway.com

set -e  # Exit on any error

echo "🚀 Starting Railway deployment for PrepLocus..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    print_error "Railway CLI is not installed. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if user is logged in to Railway
if ! railway whoami &> /dev/null; then
    print_error "You are not logged in to Railway. Please run:"
    echo "railway login"
    exit 1
fi

print_status "Checking project structure..."

# Check if essential files exist
required_files=(
    "package.json"
    "next.config.js"
    "prisma/schema.prisma"
    "railway.json"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file $file not found!"
        exit 1
    fi
done

print_success "Project structure validated"

# Check environment variables
print_status "Checking environment variables..."

required_env_vars=(
    "DATABASE_URL"
    "NEXTAUTH_SECRET"
    "NEXTAUTH_URL"
)

missing_vars=()
for var in "${required_env_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_warning "Missing environment variables: ${missing_vars[*]}"
    print_warning "Make sure to set these in Railway dashboard before deployment"
fi

# Generate Prisma client
print_status "Generating Prisma client..."
npm run prisma:generate || {
    print_error "Failed to generate Prisma client"
    exit 1
}

print_success "Prisma client generated"

# Run tests if they exist
if [ -f "package.json" ] && grep -q '"test"' package.json; then
    print_status "Running tests..."
    npm test || {
        print_warning "Tests failed, but continuing with deployment"
    }
fi

# Build the application
print_status "Building application..."
npm run build || {
    print_error "Build failed"
    exit 1
}

print_success "Application built successfully"

# Deploy to Railway
print_status "Deploying to Railway..."

# Check if project is linked
if ! railway status &> /dev/null; then
    print_error "Project is not linked to Railway. Please run:"
    echo "railway link"
    exit 1
fi

# Deploy
railway up || {
    print_error "Deployment failed"
    exit 1
}

print_success "Deployment completed!"

# Run database migrations
print_status "Running database migrations..."
railway run npx prisma migrate deploy || {
    print_warning "Database migration failed. You may need to run this manually:"
    echo "railway run npx prisma migrate deploy"
}

# Optional: Seed database
read -p "Do you want to seed the database? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Seeding database..."
    railway run npm run db:seed || {
        print_warning "Database seeding failed"
    }
fi

# Get deployment URL
deployment_url=$(railway status --json | jq -r '.deployments[0].url' 2>/dev/null || echo "")

if [ -n "$deployment_url" ]; then
    print_success "Deployment successful!"
    echo "🌐 Your application is available at: $deployment_url"
    echo "🔍 Health check: $deployment_url/api/health"
else
    print_success "Deployment completed! Check Railway dashboard for the URL."
fi

# Post-deployment checklist
echo ""
echo "📋 Post-deployment checklist:"
echo "  □ Verify all environment variables are set in Railway dashboard"
echo "  □ Test OAuth login (Google, GitHub)"
echo "  □ Test payment flow with Razorpay"
echo "  □ Verify file uploads to Bunny CDN"
echo "  □ Test email notifications"
echo "  □ Check Socket.io real-time features"
echo "  □ Monitor application logs for errors"
echo ""

print_success "Deployment script completed!"
