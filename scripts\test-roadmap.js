/**
 * Roadmap Feature Testing Script
 * 
 * This script tests the core functionality of the gamified learning roadmap feature.
 * Run with: node scripts/test-roadmap.js
 */

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function testRoadmapFeature() {
  console.log('🧪 Starting Roadmap Feature Tests...\n')
  
  try {
    // Test 1: Database Schema Validation
    console.log('📊 Test 1: Database Schema Validation')
    await testDatabaseSchema()
    
    // Test 2: Mission Creation and Management
    console.log('\n🎯 Test 2: Mission Creation and Management')
    await testMissionManagement()
    
    // Test 3: Progress Tracking
    console.log('\n📈 Test 3: Progress Tracking')
    await testProgressTracking()
    
    // Test 4: Achievement System
    console.log('\n🏆 Test 4: Achievement System')
    await testAchievementSystem()
    
    console.log('\n✅ All tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

async function testDatabaseSchema() {
  try {
    // Test if all roadmap tables exist and are accessible
    const missionCount = await prisma.courseMission.count()
    const progressCount = await prisma.missionProgress.count()
    const contentCount = await prisma.missionContent.count()
    const prerequisiteCount = await prisma.missionPrerequisite.count()
    
    console.log(`   ✓ CourseMission table: ${missionCount} records`)
    console.log(`   ✓ MissionProgress table: ${progressCount} records`)
    console.log(`   ✓ MissionContent table: ${contentCount} records`)
    console.log(`   ✓ MissionPrerequisite table: ${prerequisiteCount} records`)
    
    // Test course roadmap fields
    const coursesWithRoadmap = await prisma.course.count({
      where: { hasRoadmap: true }
    })
    console.log(`   ✓ Courses with roadmap enabled: ${coursesWithRoadmap}`)
    
  } catch (error) {
    console.error('   ❌ Database schema test failed:', error.message)
    throw error
  }
}

async function testMissionManagement() {
  try {
    // Find a test course or create one
    let testCourse = await prisma.course.findFirst({
      where: { hasRoadmap: true }
    })
    
    if (!testCourse) {
      console.log('   📝 Creating test course with roadmap...')
      testCourse = await prisma.course.create({
        data: {
          title: 'Test Roadmap Course',
          description: 'A test course for roadmap functionality',
          shortDescription: 'Test course',
          price: 0,
          categoryId: null,
          level: 'Beginner',
          language: 'English',
          duration: '1 hour',
          instructorId: 'test-instructor-id', // You'll need a valid instructor ID
          hasRoadmap: true,
          roadmapTitle: 'Test Learning Journey',
          roadmapDescription: 'A test roadmap for validation'
        }
      })
    }
    
    console.log(`   ✓ Test course: ${testCourse.title} (${testCourse.id})`)
    
    // Test mission creation
    const testMission = await prisma.courseMission.create({
      data: {
        courseId: testCourse.id,
        title: 'Test Mission 1',
        description: 'A test mission for validation',
        icon: '🎯',
        color: '#3B82F6',
        order: 1,
        isRequired: true,
        pointsReward: 100,
        estimatedTime: '30 minutes'
      }
    })
    
    console.log(`   ✓ Created test mission: ${testMission.title}`)
    
    // Test mission content assignment (if lessons exist)
    const testLesson = await prisma.courseLesson.findFirst()
    if (testLesson) {
      await prisma.missionContent.create({
        data: {
          missionId: testMission.id,
          contentId: testLesson.id,
          contentType: 'LESSON',
          order: 1,
          isRequired: true
        }
      })
      console.log(`   ✓ Assigned lesson to mission`)
    }
    
    // Clean up test data
    await prisma.missionContent.deleteMany({
      where: { missionId: testMission.id }
    })
    await prisma.courseMission.delete({
      where: { id: testMission.id }
    })
    
    if (testCourse.title === 'Test Roadmap Course') {
      await prisma.course.delete({
        where: { id: testCourse.id }
      })
    }
    
    console.log(`   ✓ Cleaned up test data`)
    
  } catch (error) {
    console.error('   ❌ Mission management test failed:', error.message)
    throw error
  }
}

async function testProgressTracking() {
  try {
    // Test progress calculation logic
    const testUserId = 'test-user-id'
    
    // Find an existing mission or skip if none
    const existingMission = await prisma.courseMission.findFirst({
      include: { contents: true }
    })
    
    if (!existingMission) {
      console.log('   ⚠️  No missions found, skipping progress tracking test')
      return
    }
    
    console.log(`   ✓ Found mission for testing: ${existingMission.title}`)
    
    // Test progress creation
    const testProgress = await prisma.missionProgress.create({
      data: {
        userId: testUserId,
        missionId: existingMission.id,
        isStarted: true,
        completionRate: 50,
        pointsEarned: 50,
        startedAt: new Date()
      }
    })
    
    console.log(`   ✓ Created test progress record`)
    
    // Test progress update
    await prisma.missionProgress.update({
      where: { id: testProgress.id },
      data: {
        completionRate: 100,
        isCompleted: true,
        pointsEarned: existingMission.pointsReward,
        completedAt: new Date()
      }
    })
    
    console.log(`   ✓ Updated progress to completed`)
    
    // Clean up
    await prisma.missionProgress.delete({
      where: { id: testProgress.id }
    })
    
    console.log(`   ✓ Cleaned up test progress data`)
    
  } catch (error) {
    console.error('   ❌ Progress tracking test failed:', error.message)
    throw error
  }
}

async function testAchievementSystem() {
  try {
    // Test achievement definitions
    const { MISSION_ACHIEVEMENT_DEFINITIONS } = require('../lib/mission-achievements')
    
    console.log(`   ✓ Found ${MISSION_ACHIEVEMENT_DEFINITIONS.length} achievement definitions`)
    
    // Test achievement condition logic
    const testStats = {
      completedMissions: 1,
      perfectMissions: 1,
      fastestMissionTime: 45,
      coursesWithMissions: 1,
      totalPointsEarned: 100,
      completedCourses: 0,
      consecutiveDays: 1,
      averageCompletionRate: 100
    }
    
    const eligibleAchievements = MISSION_ACHIEVEMENT_DEFINITIONS.filter(
      achievement => achievement.condition(testStats)
    )
    
    console.log(`   ✓ ${eligibleAchievements.length} achievements would be unlocked with test stats`)
    
    // Test notification system
    const testNotifications = await prisma.userNotification.count({
      where: {
        type: { in: ['mission_completed', 'achievement_unlocked', 'milestone_reached'] }
      }
    })
    
    console.log(`   ✓ Found ${testNotifications} mission-related notifications in system`)
    
  } catch (error) {
    console.error('   ❌ Achievement system test failed:', error.message)
    // Don't throw here as this might fail if the module isn't built yet
    console.log('   ⚠️  Achievement system test skipped (module not available)')
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  testRoadmapFeature()
}
