# Railway Deployment Troubleshooting Guide

This guide helps you diagnose and fix common issues when deploying PrepLocus to Railway.com.

## 🚨 Common Deployment Issues

### 1. Build Failures

#### Issue: "Prisma Client not found"
```
Error: Cannot find module '@prisma/client'
```

**Solution:**
```bash
# Ensure postinstall script is in package.json
"scripts": {
  "postinstall": "prisma generate"
}

# Or add to build script
"build": "prisma generate && next build"
```

#### Issue: "Module not found" errors during build
```
Error: Module not found: Can't resolve 'some-module'
```

**Solutions:**
1. Check if the module is in `dependencies` (not `devDependencies`)
2. Clear Railway build cache: Redeploy with "Clear Cache" option
3. Verify import paths are correct

#### Issue: TypeScript compilation errors
```
Type error: Property 'xyz' does not exist on type 'ABC'
```

**Solutions:**
1. Run `npm run build` locally to catch errors early
2. Ensure all TypeScript types are properly defined
3. Check if environment variables are properly typed

### 2. Database Connection Issues

#### Issue: "Database connection failed"
```
Error: P1001: Can't reach database server
```

**Solutions:**
1. Verify `DATABASE_URL` is set correctly in Railway variables
2. Ensure PostgreSQL service is running in Railway
3. Check if database migrations have been run:
   ```bash
   railway run npx prisma migrate deploy
   ```

#### Issue: "Table doesn't exist" errors
```
Error: P2021: The table 'users' does not exist in the current database
```

**Solutions:**
1. Run database migrations:
   ```bash
   railway run npx prisma migrate deploy
   ```
2. If migrations fail, check migration files in `prisma/migrations/`
3. For fresh deployment, you might need to reset:
   ```bash
   railway run npx prisma migrate reset --force
   ```

### 3. Environment Variable Issues

#### Issue: "NEXTAUTH_SECRET is not set"
```
Error: Please define a NEXTAUTH_SECRET environment variable
```

**Solutions:**
1. Generate a secure secret:
   ```bash
   openssl rand -base64 32
   ```
2. Add to Railway variables (without quotes)
3. Ensure variable name is exactly `NEXTAUTH_SECRET`

#### Issue: OAuth providers not working
```
Error: Invalid client_id or client_secret
```

**Solutions:**
1. Verify OAuth app configuration in Google/GitHub console
2. Ensure redirect URIs include your Railway domain
3. Check environment variables are set correctly:
   - `GOOGLE_CLIENT_ID`
   - `GOOGLE_CLIENT_SECRET`
   - `GITHUB_CLIENT_ID`
   - `GITHUB_CLIENT_SECRET`

### 4. Socket.io Connection Issues

#### Issue: Socket connections failing
```
Error: WebSocket connection failed
```

**Solutions:**
1. Ensure Socket server is deployed as separate service
2. Check `NEXT_PUBLIC_SOCKET_URL` points to correct Railway URL
3. Verify CORS configuration in socket server
4. Test socket health endpoint: `https://your-socket-server.railway.app/health`

#### Issue: "Transport unknown" errors
```
Error: Transport "websocket" not supported
```

**Solutions:**
1. Enable polling fallback in socket configuration:
   ```javascript
   transports: ['websocket', 'polling']
   ```
2. Check Railway proxy configuration
3. Verify client-side socket configuration

### 5. File Upload Issues

#### Issue: Bunny CDN uploads failing
```
Error: 401 Unauthorized - Invalid access key
```

**Solutions:**
1. Verify Bunny CDN credentials:
   - `BUNNY_STORAGE_ZONE_NAME`
   - `BUNNY_STORAGE_ACCESS_KEY`
   - `BUNNY_PULL_ZONE_URL`
2. Test credentials with curl:
   ```bash
   curl -X PUT "https://storage.bunnycdn.com/your-zone/test.txt" \
        -H "AccessKey: your-access-key" \
        -d "test content"
   ```

### 6. Payment Integration Issues

#### Issue: Razorpay orders not creating
```
Error: Invalid key_id or key_secret
```

**Solutions:**
1. Verify Razorpay credentials are for correct environment (test vs live)
2. Check environment variables:
   - `RAZORPAY_KEY_ID`
   - `RAZORPAY_KEY_SECRET`
   - `NEXT_PUBLIC_RAZORPAY_KEY_ID`
3. Test with Razorpay dashboard

## 🔍 Debugging Techniques

### 1. Check Railway Logs

```bash
# View deployment logs
railway logs

# Follow live logs
railway logs --follow

# Filter logs by service
railway logs --service your-service-name
```

### 2. Health Check Endpoints

Test these endpoints to verify services:

```bash
# Main application health
curl https://your-app.railway.app/api/health

# Socket server health
curl https://your-socket-server.railway.app/health

# Database connectivity test
railway run npx prisma db pull
```

### 3. Environment Variable Verification

```bash
# List all environment variables
railway variables

# Check specific variable
railway run echo $DATABASE_URL
```

### 4. Database Debugging

```bash
# Connect to database
railway connect postgres

# Check database schema
railway run npx prisma db pull

# View migration status
railway run npx prisma migrate status
```

## 🛠️ Performance Issues

### 1. Slow Application Response

**Symptoms:**
- High response times
- Timeouts
- Memory errors

**Solutions:**
1. Check Railway metrics in dashboard
2. Optimize database queries:
   ```typescript
   // Add database indexes
   CREATE INDEX idx_course_enrollments_user_id ON course_enrollments(user_id);
   ```
3. Enable Next.js optimizations:
   ```javascript
   // next.config.js
   const nextConfig = {
     compress: true,
     experimental: {
       optimizeCss: true
     }
   }
   ```

### 2. Database Performance

**Solutions:**
1. Add connection pooling:
   ```typescript
   const prisma = new PrismaClient({
     datasources: {
       db: {
         url: process.env.DATABASE_URL + '?connection_limit=5'
       }
     }
   })
   ```
2. Optimize queries with proper indexes
3. Use Railway's database metrics to identify slow queries

### 3. Memory Issues

**Symptoms:**
```
Error: JavaScript heap out of memory
```

**Solutions:**
1. Increase Node.js memory limit:
   ```json
   "scripts": {
     "start": "node --max-old-space-size=1024 server.js"
   }
   ```
2. Optimize image handling and file uploads
3. Implement proper garbage collection

## 🔧 Railway-Specific Solutions

### 1. Service Configuration

**Correct service setup:**
```json
// railway.json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "healthcheckPath": "/api/health",
    "healthcheckTimeout": 30
  }
}
```

### 2. Multiple Services Setup

For Socket.io server as separate service:

1. **Main App Service:**
   - Repository: Your main repo
   - Build Command: `npm run build`
   - Start Command: `npm run start`

2. **Socket Server Service:**
   - Repository: Same repo
   - Build Command: `npm install`
   - Start Command: `npm run start:socket:railway`

### 3. Domain Configuration

**For custom domains:**
1. Add domain in Railway dashboard
2. Update DNS records:
   ```
   Type: CNAME
   Name: @
   Value: your-app.railway.app
   ```
3. Update environment variables:
   ```
   NEXTAUTH_URL=https://yourdomain.com
   ```

## 📞 Getting Help

### 1. Railway Support Channels

- **Railway Discord**: [discord.gg/railway](https://discord.gg/railway)
- **Railway Documentation**: [docs.railway.app](https://docs.railway.app)
- **Railway Status**: [status.railway.app](https://status.railway.app)

### 2. Application-Specific Support

- **Next.js Issues**: [nextjs.org/docs](https://nextjs.org/docs)
- **Prisma Issues**: [prisma.io/docs](https://prisma.io/docs)
- **Socket.io Issues**: [socket.io/docs](https://socket.io/docs)

### 3. Debugging Information to Collect

When seeking help, provide:

1. **Railway logs:**
   ```bash
   railway logs > deployment-logs.txt
   ```

2. **Environment variables list:**
   ```bash
   railway variables > env-vars.txt
   ```

3. **Package.json and relevant config files**

4. **Error messages and stack traces**

5. **Steps to reproduce the issue**

## ✅ Prevention Checklist

Before deploying:

- [ ] Test build locally: `npm run build`
- [ ] Verify all environment variables are set
- [ ] Test database connection: `npx prisma db pull`
- [ ] Run migrations: `npx prisma migrate deploy`
- [ ] Test OAuth providers in development
- [ ] Verify file upload functionality
- [ ] Test payment flow (if applicable)
- [ ] Check health endpoints
- [ ] Monitor Railway metrics after deployment

## 🚀 Quick Recovery Steps

If deployment fails completely:

1. **Rollback to previous deployment:**
   ```bash
   railway rollback
   ```

2. **Check service status:**
   ```bash
   railway status
   ```

3. **Restart services:**
   ```bash
   railway restart
   ```

4. **Clear build cache and redeploy:**
   - Go to Railway dashboard
   - Select service
   - Click "Deploy" → "Clear Cache"

Remember: Most Railway deployment issues are related to environment variables, database connections, or build configuration. Start with these areas when troubleshooting.
