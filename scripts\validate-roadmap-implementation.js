#!/usr/bin/env node

/**
 * Roadmap Implementation Validation Script
 * 
 * This script validates the roadmap implementation for common issues
 */

const fs = require('fs')
const path = require('path')

function validateRoadmapImplementation() {
  console.log('🔍 Validating Roadmap Implementation...\n')
  
  const issues = []
  const warnings = []

  try {
    // Check 1: Database Schema
    validateDatabaseSchema(issues, warnings)
    
    // Check 2: API Endpoints
    validateAPIEndpoints(issues, warnings)
    
    // Check 3: Frontend Components
    validateFrontendComponents(issues, warnings)
    
    // Check 4: Type Definitions
    validateTypeDefinitions(issues, warnings)
    
    // Check 5: Error Handling
    validateErrorHandling(issues, warnings)
    
    // Summary
    console.log('\n' + '='.repeat(60))
    console.log('📊 VALIDATION SUMMARY')
    console.log('='.repeat(60))
    console.log(`🚨 Issues Found: ${issues.length}`)
    console.log(`⚠️  Warnings: ${warnings.length}`)
    
    if (issues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:')
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`)
      })
    }
    
    if (warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:')
      warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`)
      })
    }
    
    if (issues.length === 0 && warnings.length === 0) {
      console.log('\n🎉 All validations passed! The roadmap implementation looks good.')
    } else if (issues.length === 0) {
      console.log('\n✅ No critical issues found. Please review warnings.')
    } else {
      console.log('\n❌ Critical issues found. Please fix before deployment.')
    }
    
  } catch (error) {
    console.error('💥 Validation failed:', error)
  }
}

function validateDatabaseSchema(issues, warnings) {
  console.log('🗄️  Validating Database Schema...')
  
  try {
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma')
    const schema = fs.readFileSync(schemaPath, 'utf8')
    
    // Check for required models
    const requiredModels = ['CourseMission', 'MissionProgress', 'MissionContent', 'MissionPrerequisite']
    requiredModels.forEach(model => {
      if (!schema.includes(`model ${model}`)) {
        issues.push(`Missing database model: ${model}`)
      } else {
        console.log(`     ✓ Model ${model} exists`)
      }
    })
    
    // Check for roadmap fields in Course model
    const roadmapFields = ['hasRoadmap', 'roadmapTitle', 'roadmapDescription']
    roadmapFields.forEach(field => {
      if (!schema.includes(field)) {
        issues.push(`Missing roadmap field in Course model: ${field}`)
      } else {
        console.log(`     ✓ Course field ${field} exists`)
      }
    })
    
    // Check for proper relations
    if (!schema.includes('missions            CourseMission[]')) {
      warnings.push('Course to CourseMission relation might be missing or incorrectly named')
    }
    
  } catch (error) {
    issues.push(`Failed to read database schema: ${error.message}`)
  }
}

function validateAPIEndpoints(issues, warnings) {
  console.log('🌐 Validating API Endpoints...')
  
  const endpoints = [
    {
      path: 'app/api/admin/courses/[id]/roadmap/route.ts',
      name: 'Admin Roadmap API',
      requiredMethods: ['GET', 'PUT']
    },
    {
      path: 'app/api/student/courses/[slug]/roadmap/route.ts',
      name: 'Student Roadmap API',
      requiredMethods: ['GET']
    },
    {
      path: 'app/api/student/courses/[slug]/roadmap/missions/[missionId]/start/route.ts',
      name: 'Mission Start API',
      requiredMethods: ['POST']
    }
  ]
  
  endpoints.forEach(endpoint => {
    try {
      const filePath = path.join(process.cwd(), endpoint.path)
      if (!fs.existsSync(filePath)) {
        issues.push(`Missing API endpoint: ${endpoint.name} (${endpoint.path})`)
        return
      }
      
      const content = fs.readFileSync(filePath, 'utf8')
      
      endpoint.requiredMethods.forEach(method => {
        if (!content.includes(`export const ${method}`)) {
          issues.push(`Missing ${method} method in ${endpoint.name}`)
        } else {
          console.log(`     ✓ ${endpoint.name} ${method} method exists`)
        }
      })
      
      // Check for proper error handling
      if (!content.includes('try {') || !content.includes('catch')) {
        warnings.push(`${endpoint.name} might be missing error handling`)
      }
      
      // Check for authentication
      if (!content.includes('requireAuth: true')) {
        warnings.push(`${endpoint.name} might be missing authentication`)
      }
      
    } catch (error) {
      issues.push(`Failed to validate ${endpoint.name}: ${error.message}`)
    }
  })
}

function validateFrontendComponents(issues, warnings) {
  console.log('⚛️  Validating Frontend Components...')
  
  const components = [
    {
      path: 'components/admin/roadmap/roadmap-config.tsx',
      name: 'Admin Roadmap Config'
    },
    {
      path: 'components/student/roadmap/roadmap-visualization.tsx',
      name: 'Student Roadmap Visualization'
    },
    {
      path: 'app/admin/courses/[id]/roadmap/page.tsx',
      name: 'Admin Roadmap Page'
    },
    {
      path: 'app/student/courses/[slug]/roadmap/page.tsx',
      name: 'Student Roadmap Page'
    }
  ]
  
  components.forEach(component => {
    try {
      const filePath = path.join(process.cwd(), component.path)
      if (!fs.existsSync(filePath)) {
        issues.push(`Missing component: ${component.name} (${component.path})`)
        return
      }
      
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for React imports
      if (!content.includes("import React") && !content.includes("'use client'")) {
        warnings.push(`${component.name} might be missing React imports or client directive`)
      }
      
      // Check for error handling
      if (!content.includes('try {') && !content.includes('catch')) {
        warnings.push(`${component.name} might be missing error handling`)
      }
      
      console.log(`     ✓ Component ${component.name} exists`)
      
    } catch (error) {
      issues.push(`Failed to validate ${component.name}: ${error.message}`)
    }
  })
}

function validateTypeDefinitions(issues, warnings) {
  console.log('📝 Validating Type Definitions...')
  
  const typeFiles = [
    'components/admin/roadmap/roadmap-config.tsx',
    'components/student/roadmap/roadmap-visualization.tsx',
    'app/api/admin/courses/[id]/roadmap/route.ts',
    'app/api/student/courses/[slug]/roadmap/route.ts'
  ]
  
  typeFiles.forEach(file => {
    try {
      const filePath = path.join(process.cwd(), file)
      if (!fs.existsSync(filePath)) return
      
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for interface definitions
      if (content.includes('interface Mission') || content.includes('type Mission')) {
        console.log(`     ✓ Mission types defined in ${path.basename(file)}`)
      }
      
      // Check for proper typing
      if (content.includes(': any') && !content.includes('// @ts-ignore')) {
        warnings.push(`${path.basename(file)} contains 'any' types - consider more specific typing`)
      }
      
    } catch (error) {
      warnings.push(`Failed to validate types in ${file}: ${error.message}`)
    }
  })
}

function validateErrorHandling(issues, warnings) {
  console.log('🛡️  Validating Error Handling...')
  
  const criticalFiles = [
    'app/api/admin/courses/[id]/roadmap/route.ts',
    'app/api/student/courses/[slug]/roadmap/route.ts',
    'components/admin/roadmap/roadmap-config.tsx',
    'app/student/courses/[slug]/roadmap/page.tsx'
  ]
  
  criticalFiles.forEach(file => {
    try {
      const filePath = path.join(process.cwd(), file)
      if (!fs.existsSync(filePath)) return
      
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for try-catch blocks
      const tryCount = (content.match(/try\s*{/g) || []).length
      const catchCount = (content.match(/catch\s*\(/g) || []).length
      
      if (tryCount !== catchCount) {
        warnings.push(`${path.basename(file)} has unmatched try-catch blocks`)
      }
      
      // Check for error logging
      if (content.includes('catch') && !content.includes('console.error')) {
        warnings.push(`${path.basename(file)} might be missing error logging`)
      }
      
      console.log(`     ✓ Error handling checked in ${path.basename(file)}`)
      
    } catch (error) {
      warnings.push(`Failed to validate error handling in ${file}: ${error.message}`)
    }
  })
}

// Run validation if script is executed directly
if (require.main === module) {
  validateRoadmapImplementation()
}

module.exports = { validateRoadmapImplementation }
