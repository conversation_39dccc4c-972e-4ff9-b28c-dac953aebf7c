'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { X, Plus, Search, GripVertical } from 'lucide-react'
import { toast } from 'sonner'
import BundleImageUpload from '@/components/admin/quiz-bundles/bundle-image-upload'

const bundleSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be non-negative'),
  originalPrice: z.number().optional(),
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  thumbnailImage: z.string().url().optional().or(z.literal('')),
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  duration: z.string().optional(),
  isActive: z.boolean().optional(),
  isPublished: z.boolean().optional()
})

type BundleFormData = z.infer<typeof bundleSchema>

interface Quiz {
  id: string
  title: string
  type: string
  difficulty: string
  timeLimit?: number
  category?: string
  tags: string[]
}

interface BundleFormProps {
  bundle?: any
  onSubmit: (data: BundleFormData & { quizIds: string[], tags: string[], features: string[] }) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function BundleForm({ bundle, onSubmit, onCancel, isLoading }: BundleFormProps) {
  const [availableQuizzes, setAvailableQuizzes] = useState<Quiz[]>([])
  const [selectedQuizzes, setSelectedQuizzes] = useState<Quiz[]>([])
  const [quizSearch, setQuizSearch] = useState('')
  const [tags, setTags] = useState<string[]>(bundle?.tags || [])
  const [features, setFeatures] = useState<string[]>(bundle?.features || [])
  const [newTag, setNewTag] = useState('')
  const [newFeature, setNewFeature] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<BundleFormData>({
    resolver: zodResolver(bundleSchema),
    defaultValues: bundle ? {
      title: bundle.title,
      description: bundle.description,
      shortDescription: bundle.shortDescription,
      price: bundle.price,
      originalPrice: bundle.originalPrice,
      slug: bundle.slug,
      thumbnailImage: bundle.thumbnailImage,
      category: bundle.category,
      level: bundle.level,
      duration: bundle.duration,
      isActive: bundle.isActive,
      isPublished: bundle.isPublished
    } : {
      isActive: true,
      isPublished: false,
      price: 0
    }
  })

  const title = watch('title')

  // Auto-generate slug from title
  useEffect(() => {
    if (title && !bundle) {
      const slug = title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setValue('slug', slug)
    }
  }, [title, setValue, bundle])

  // Fetch available quizzes
  useEffect(() => {
    fetchQuizzes()
  }, [])

  // Set selected quizzes if editing
  useEffect(() => {
    if (bundle?.quizzes) {
      setSelectedQuizzes(bundle.quizzes)
    }
  }, [bundle])

  const fetchQuizzes = async () => {
    try {
      const response = await fetch('/api/admin/quizzes?status=published&limit=100')
      if (response.ok) {
        const data = await response.json()

        // The admin quizzes API returns { quizzes: [...], success: true }
        if (data.success && data.quizzes) {
          setAvailableQuizzes(data.quizzes)
        } else {
          console.error('Unexpected API response structure:', data)
          setAvailableQuizzes([])
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Failed to fetch quizzes:', response.status, response.statusText, errorData)
        toast.error(errorData.error || 'Failed to load quizzes')
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to load quizzes')
    }
  }

  const addQuiz = (quiz: Quiz) => {
    if (!selectedQuizzes.find(q => q.id === quiz.id)) {
      setSelectedQuizzes([...selectedQuizzes, quiz])
    }
  }

  const removeQuiz = (quizId: string) => {
    setSelectedQuizzes(selectedQuizzes.filter(q => q.id !== quizId))
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag))
  }

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()])
      setNewFeature('')
    }
  }

  const removeFeature = (feature: string) => {
    setFeatures(features.filter(f => f !== feature))
  }

  const filteredQuizzes = availableQuizzes.filter(quiz =>
    quiz.title.toLowerCase().includes(quizSearch.toLowerCase()) ||
    quiz.category?.toLowerCase().includes(quizSearch.toLowerCase()) ||
    quiz.tags.some(tag => tag.toLowerCase().includes(quizSearch.toLowerCase()))
  )

  const handleThumbnailUpload = async (imageData: any) => {
    // Custom upload handler for quiz bundle thumbnails
    console.log('Thumbnail uploaded:', imageData)
    toast.success('Thumbnail uploaded successfully!')
  }

  const handleFormSubmit = async (formData: any) => {
    if (selectedQuizzes.length === 0) {
      toast.error('Please select at least one quiz')
      return
    }

    try {
      const data = formData as BundleFormData
      await onSubmit({
        ...data,
        quizIds: selectedQuizzes.map(q => q.id),
        tags,
        features
      })
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('Failed to submit form')
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Enter bundle title"
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                {...register('slug')}
                placeholder="bundle-slug"
              />
              {errors.slug && (
                <p className="text-sm text-red-600 mt-1">{errors.slug.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="shortDescription">Short Description</Label>
            <Input
              id="shortDescription"
              {...register('shortDescription')}
              placeholder="Brief description for cards"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Detailed description of the bundle"
              rows={4}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="category">Category</Label>
              <Input
                id="category"
                {...register('category')}
                placeholder="e.g., Mathematics, Science"
              />
            </div>

            <div>
              <Label htmlFor="level">Level</Label>
              <Select
                value={watch('level') || ''}
                onValueChange={(value) => setValue('level', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Beginner">Beginner</SelectItem>
                  <SelectItem value="Intermediate">Intermediate</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="duration">Duration</Label>
              <Input
                id="duration"
                {...register('duration')}
                placeholder="e.g., 2 weeks, 30 hours"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Thumbnail Image */}
      <Card>
        <CardHeader>
          <CardTitle>Thumbnail Image</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label>Bundle Thumbnail</Label>
            <p className="text-sm text-gray-500 mb-4">
              Upload an attractive thumbnail image for your quiz bundle. Recommended size: 1280x720 pixels.
            </p>
            <BundleImageUpload
              existingImage={watch('thumbnailImage') || ''}
              onImageChange={(imageUrl: string | null) => setValue('thumbnailImage', imageUrl || '')}
              onImageUpload={handleThumbnailUpload}
              aspectRatio="video"
              placeholder="Upload bundle thumbnail (16:9 recommended)"
              bundleId={bundle?.id || 'new-bundle'}
              imageType="thumbnail"
            />
          </div>
        </CardContent>
      </Card>

      {/* Pricing */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="price">Price (₹) *</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                {...register('price', { valueAsNumber: true })}
                placeholder="0.00"
              />
              {errors.price && (
                <p className="text-sm text-red-600 mt-1">{errors.price.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="originalPrice">Original Price (₹)</Label>
              <Input
                id="originalPrice"
                type="number"
                step="0.01"
                {...register('originalPrice', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quiz Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Quiz Selection</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Search Quizzes</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search quizzes..."
                value={quizSearch}
                onChange={(e) => setQuizSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Available Quizzes */}
            <div>
              <Label>Available Quizzes</Label>
              <div className="border rounded-lg p-4 h-64 overflow-y-auto">
                {filteredQuizzes.map(quiz => (
                  <div
                    key={quiz.id}
                    className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer"
                    onClick={() => addQuiz(quiz)}
                  >
                    <div>
                      <p className="font-medium text-sm">{quiz.title}</p>
                      <p className="text-xs text-gray-500">
                        {quiz.type} • {quiz.difficulty}
                      </p>
                    </div>
                    <Plus className="h-4 w-4 text-gray-400" />
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Quizzes */}
            <div>
              <Label>Selected Quizzes ({selectedQuizzes.length})</Label>
              <div className="border rounded-lg p-4 h-64 overflow-y-auto">
                {selectedQuizzes.map((quiz, index) => (
                  <div
                    key={quiz.id}
                    className="flex items-center justify-between p-2 bg-blue-50 rounded mb-2"
                  >
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="font-medium text-sm">{quiz.title}</p>
                        <p className="text-xs text-gray-500">Order: {index + 1}</p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeQuiz(quiz.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {selectedQuizzes.length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    No quizzes selected
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tags and Features */}
      <Card>
        <CardHeader>
          <CardTitle>Tags & Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Tags</Label>
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Add tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag}>Add</Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {tags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label>Features</Label>
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Add feature"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
              />
              <Button type="button" onClick={addFeature}>Add</Button>
            </div>
            <div className="space-y-2">
              {features.map(feature => (
                <div key={feature} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm">{feature}</span>
                  <X className="h-4 w-4 cursor-pointer text-gray-400" onClick={() => removeFeature(feature)} />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Active</Label>
              <p className="text-sm text-gray-500">Bundle is available for management</p>
            </div>
            <Switch
              checked={watch('isActive') || false}
              onCheckedChange={(checked) => setValue('isActive', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Published</Label>
              <p className="text-sm text-gray-500">Bundle is visible to students</p>
            </div>
            <Switch
              checked={watch('isPublished') || false}
              onCheckedChange={(checked) => setValue('isPublished', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : bundle ? 'Update Bundle' : 'Create Bundle'}
        </Button>
      </div>
    </form>
  )
}
