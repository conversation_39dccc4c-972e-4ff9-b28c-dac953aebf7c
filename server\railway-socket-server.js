const { createServer } = require('http')
const { Server } = require('socket.io')
const { PrismaClient } = require('../lib/generated/prisma')

// Initialize Prisma client
const prisma = new PrismaClient()

// Get port from Railway environment or default
const port = process.env.PORT || 3001
const isDev = process.env.NODE_ENV !== 'production'

// Store connected users and active rooms
const connectedUsers = new Map()
const activeRooms = new Map()

// Create HTTP server
const httpServer = createServer((req, res) => {
  // Health check endpoint for Railway
  if (req.url === '/health' || req.url === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'socket-server',
      port: port,
      connectedUsers: connectedUsers.size,
      activeRooms: activeRooms.size
    }))
    return
  }
  
  // Default response
  res.writeHead(200, { 'Content-Type': 'text/plain' })
  res.end('PrepLocus Socket Server is running')
})

// Initialize Socket.IO server
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXTAUTH_URL || (isDev ? "http://localhost:3000" : "*"),
    methods: ["GET", "POST"],
    credentials: true
  },
  path: '/socket.io',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
})

// Socket connection handling
io.on('connection', (socket) => {
  console.log('🔌 New socket connection:', socket.id)

  // Handle user authentication
  socket.on('authenticate', async (data) => {
    try {
      const { userId, userRole, userName } = data
      
      if (userId) {
        connectedUsers.set(socket.id, {
          userId,
          userRole,
          userName,
          connectedAt: new Date()
        })
        
        socket.userId = userId
        socket.userRole = userRole
        socket.userName = userName
        
        // Join user-specific room
        socket.join(`user:${userId}`)
        
        // Join role-specific room
        if (userRole) {
          socket.join(`role:${userRole}`)
        }
        
        console.log(`✅ User authenticated: ${userName} (${userId}) - ${userRole}`)
        
        socket.emit('authenticated', {
          success: true,
          message: 'Successfully authenticated'
        })
      }
    } catch (error) {
      console.error('Authentication error:', error)
      socket.emit('authentication_error', {
        success: false,
        message: 'Authentication failed'
      })
    }
  })

  // Handle joining specific rooms (courses, quizzes, etc.)
  socket.on('join_room', (data) => {
    try {
      const { roomId, roomType } = data
      const roomKey = `${roomType}:${roomId}`
      
      socket.join(roomKey)
      
      // Track active rooms
      if (!activeRooms.has(roomKey)) {
        activeRooms.set(roomKey, new Set())
      }
      activeRooms.get(roomKey).add(socket.id)
      
      console.log(`📍 User ${socket.userName || socket.id} joined room: ${roomKey}`)
      
      socket.emit('room_joined', {
        roomId,
        roomType,
        success: true
      })
      
      // Notify others in the room
      socket.to(roomKey).emit('user_joined_room', {
        userId: socket.userId,
        userName: socket.userName,
        roomId,
        roomType
      })
    } catch (error) {
      console.error('Error joining room:', error)
      socket.emit('room_error', {
        success: false,
        message: 'Failed to join room'
      })
    }
  })

  // Handle leaving rooms
  socket.on('leave_room', (data) => {
    try {
      const { roomId, roomType } = data
      const roomKey = `${roomType}:${roomId}`
      
      socket.leave(roomKey)
      
      // Update active rooms tracking
      if (activeRooms.has(roomKey)) {
        activeRooms.get(roomKey).delete(socket.id)
        if (activeRooms.get(roomKey).size === 0) {
          activeRooms.delete(roomKey)
        }
      }
      
      console.log(`📤 User ${socket.userName || socket.id} left room: ${roomKey}`)
      
      // Notify others in the room
      socket.to(roomKey).emit('user_left_room', {
        userId: socket.userId,
        userName: socket.userName,
        roomId,
        roomType
      })
    } catch (error) {
      console.error('Error leaving room:', error)
    }
  })

  // Handle real-time notifications
  socket.on('send_notification', async (data) => {
    try {
      if (socket.userRole !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized to send notifications' })
        return
      }

      const { targetUsers, notification } = data
      
      // Send to specific users or broadcast
      if (targetUsers && targetUsers.length > 0) {
        targetUsers.forEach(userId => {
          io.to(`user:${userId}`).emit('notification', notification)
        })
      } else {
        // Broadcast to all connected users
        io.emit('notification', notification)
      }
      
      console.log(`📢 Notification sent by ${socket.userName}:`, notification.title)
    } catch (error) {
      console.error('Error sending notification:', error)
      socket.emit('error', { message: 'Failed to send notification' })
    }
  })

  // Handle live quiz events
  socket.on('quiz_event', (data) => {
    try {
      const { quizId, eventType, eventData } = data
      const roomKey = `quiz:${quizId}`
      
      // Broadcast quiz event to all participants
      socket.to(roomKey).emit('quiz_event', {
        eventType,
        eventData,
        timestamp: new Date().toISOString(),
        fromUser: {
          id: socket.userId,
          name: socket.userName
        }
      })
      
      console.log(`🎯 Quiz event (${eventType}) in quiz ${quizId} by ${socket.userName}`)
    } catch (error) {
      console.error('Error handling quiz event:', error)
    }
  })

  // Handle course progress updates
  socket.on('progress_update', async (data) => {
    try {
      const { courseId, lessonId, progress } = data
      
      if (!socket.userId) {
        socket.emit('error', { message: 'User not authenticated' })
        return
      }
      
      // Update progress in database
      await prisma.courseProgress.upsert({
        where: {
          userId_lessonId: {
            userId: socket.userId,
            lessonId: lessonId
          }
        },
        update: {
          watchTime: progress.watchTime || 0,
          lastPosition: progress.lastPosition || 0,
          isCompleted: progress.isCompleted || false,
          lastAccessAt: new Date()
        },
        create: {
          userId: socket.userId,
          lessonId: lessonId,
          watchTime: progress.watchTime || 0,
          lastPosition: progress.lastPosition || 0,
          isCompleted: progress.isCompleted || false,
          firstAccessAt: new Date(),
          lastAccessAt: new Date()
        }
      })
      
      // Notify course room about progress update
      const courseRoomKey = `course:${courseId}`
      socket.to(courseRoomKey).emit('progress_updated', {
        userId: socket.userId,
        userName: socket.userName,
        lessonId,
        progress
      })
      
      console.log(`📈 Progress updated for user ${socket.userName} in lesson ${lessonId}`)

      // Check if this lesson is part of any missions and update mission progress
      try {
        await updateMissionProgressForContent(socket.userId, lessonId, 'LESSON', progress.isCompleted || false)
      } catch (missionError) {
        console.error('Error updating mission progress:', missionError)
        // Don't fail the main progress update if mission update fails
      }
    } catch (error) {
      console.error('Error updating progress:', error)
      socket.emit('error', { message: 'Failed to update progress' })
    }
  })

  // Handle mission progress updates
  socket.on('mission_progress_update', async (data) => {
    try {
      const { missionId, contentId, contentType, isCompleted, courseId } = data

      if (!socket.userId) {
        socket.emit('error', { message: 'User not authenticated' })
        return
      }

      const result = await updateMissionProgressForContent(
        socket.userId,
        contentId,
        contentType,
        isCompleted,
        missionId,
        courseId
      )

      if (result) {
        // Emit mission progress update to user
        socket.emit('mission_progress_updated', {
          missionId,
          progress: result.missionProgress,
          missionCompleted: result.missionCompleted,
          achievementsUnlocked: result.achievementsUnlocked,
          nextMissions: result.nextMissions
        })

        // Notify course room about mission completion
        if (result.missionCompleted) {
          const courseRoomKey = `course:${courseId}`
          socket.to(courseRoomKey).emit('mission_completed', {
            userId: socket.userId,
            userName: socket.userName,
            missionId,
            pointsEarned: result.missionProgress.pointsEarned
          })

          console.log(`🎯 Mission ${missionId} completed by user ${socket.userName}`)

          // Handle achievements and notifications
          try {
            await handleMissionCompletionRewards(socket.userId, missionId, courseId, result.missionProgress.pointsEarned)
          } catch (rewardError) {
            console.error('Error handling mission completion rewards:', rewardError)
          }
        }
      }
    } catch (error) {
      console.error('Error updating mission progress:', error)
      socket.emit('error', { message: 'Failed to update mission progress' })
    }
  })

  // Handle typing indicators for discussions
  socket.on('typing_start', (data) => {
    const { roomId, roomType } = data
    const roomKey = `${roomType}:${roomId}`
    
    socket.to(roomKey).emit('user_typing', {
      userId: socket.userId,
      userName: socket.userName
    })
  })

  socket.on('typing_stop', (data) => {
    const { roomId, roomType } = data
    const roomKey = `${roomType}:${roomId}`
    
    socket.to(roomKey).emit('user_stopped_typing', {
      userId: socket.userId,
      userName: socket.userName
    })
  })

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`🔌 Socket disconnected: ${socket.id} (${socket.userName || 'Unknown'}) - Reason: ${reason}`)
    
    // Clean up user data
    connectedUsers.delete(socket.id)
    
    // Clean up room tracking
    activeRooms.forEach((users, roomKey) => {
      if (users.has(socket.id)) {
        users.delete(socket.id)
        if (users.size === 0) {
          activeRooms.delete(roomKey)
        } else {
          // Notify others in the room
          socket.to(roomKey).emit('user_disconnected', {
            userId: socket.userId,
            userName: socket.userName
          })
        }
      }
    })
  })

  // Handle connection errors
  socket.on('error', (error) => {
    console.error('Socket error:', error)
  })
})

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...')
  
  // Close all socket connections
  io.close(() => {
    console.log('✅ Socket.IO server closed')
    
    // Close database connection
    prisma.$disconnect().then(() => {
      console.log('✅ Database connection closed')
      process.exit(0)
    })
  })
})

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...')
  
  io.close(() => {
    console.log('✅ Socket.IO server closed')
    
    prisma.$disconnect().then(() => {
      console.log('✅ Database connection closed')
      process.exit(0)
    })
  })
})

// Helper function to update mission progress
async function updateMissionProgressForContent(userId, contentId, contentType, isCompleted, missionId = null, courseId = null) {
  try {
    // Find missions that contain this content
    let missionsToUpdate = []

    if (missionId && courseId) {
      // Specific mission update
      const mission = await prisma.courseMission.findUnique({
        where: { id: missionId },
        include: { contents: true }
      })
      if (mission && mission.contents.some(c => c.contentId === contentId)) {
        missionsToUpdate.push(mission)
      }
    } else {
      // Find all missions containing this content
      const missions = await prisma.courseMission.findMany({
        where: {
          contents: {
            some: {
              contentId: contentId,
              contentType: contentType
            }
          }
        },
        include: { contents: true }
      })
      missionsToUpdate = missions
    }

    if (missionsToUpdate.length === 0) {
      return null
    }

    // Update progress for each mission
    let lastResult = null
    for (const mission of missionsToUpdate) {
      // Calculate completion rate
      const completedContents = await getCompletedMissionContents(userId, mission.contents)
      const totalRequiredContents = mission.contents.filter(c => c.isRequired).length
      const completedRequiredContents = completedContents.filter(c =>
        mission.contents.find(mc => mc.contentId === c.contentId && mc.isRequired)
      ).length

      const completionRate = totalRequiredContents > 0
        ? (completedRequiredContents / totalRequiredContents) * 100
        : 0

      const isMissionCompleted = completionRate >= 100

      // Get or create mission progress
      let missionProgress = await prisma.missionProgress.findUnique({
        where: {
          userId_missionId: {
            userId,
            missionId: mission.id
          }
        }
      })

      if (!missionProgress) {
        missionProgress = await prisma.missionProgress.create({
          data: {
            userId,
            missionId: mission.id,
            isStarted: true,
            startedAt: new Date(),
            lastAccessAt: new Date()
          }
        })
      }

      // Update mission progress
      const updatedProgress = await prisma.missionProgress.update({
        where: { id: missionProgress.id },
        data: {
          completionRate,
          isCompleted: isMissionCompleted,
          pointsEarned: isMissionCompleted ? mission.pointsReward : Math.floor((completionRate / 100) * mission.pointsReward),
          completedAt: isMissionCompleted && !missionProgress.isCompleted ? new Date() : missionProgress.completedAt,
          lastAccessAt: new Date()
        }
      })

      lastResult = {
        missionProgress: {
          id: updatedProgress.id,
          isStarted: updatedProgress.isStarted,
          isCompleted: updatedProgress.isCompleted,
          completionRate: updatedProgress.completionRate,
          pointsEarned: updatedProgress.pointsEarned,
          startedAt: updatedProgress.startedAt,
          completedAt: updatedProgress.completedAt
        },
        missionCompleted: isMissionCompleted && !missionProgress.isCompleted,
        achievementsUnlocked: mission.badgeReward && isMissionCompleted ? [mission.badgeReward] : [],
        nextMissions: []
      }
    }

    return lastResult
  } catch (error) {
    console.error('Error in updateMissionProgressForContent:', error)
    throw error
  }
}

// Helper function to get completed mission contents
async function getCompletedMissionContents(userId, missionContents) {
  const completedContents = []

  for (const content of missionContents) {
    let isCompleted = false

    switch (content.contentType) {
      case 'LESSON':
        const lessonProgress = await prisma.courseProgress.findUnique({
          where: {
            userId_lessonId: {
              userId,
              lessonId: content.contentId
            }
          }
        })
        isCompleted = lessonProgress?.isCompleted || false
        break

      case 'QUIZ':
        const quizAttempt = await prisma.courseQuizAttempt.findFirst({
          where: {
            userId,
            quizId: content.contentId,
            isCompleted: true
          },
          orderBy: { completedAt: 'desc' }
        })
        isCompleted = !!quizAttempt
        break

      default:
        isCompleted = false
    }

    if (isCompleted) {
      completedContents.push({
        contentId: content.contentId,
        contentType: content.contentType
      })
    }
  }

  return completedContents
}

// Helper function to handle mission completion rewards
async function handleMissionCompletionRewards(userId, missionId, courseId, pointsEarned) {
  try {
    // Get mission details
    const mission = await prisma.courseMission.findUnique({
      where: { id: missionId },
      include: {
        course: {
          select: { title: true }
        }
      }
    })

    if (!mission) return

    // Check if this is the user's first mission
    const userMissionCount = await prisma.missionProgress.count({
      where: {
        userId,
        isCompleted: true
      }
    })

    const isFirstMission = userMissionCount === 1

    // Calculate current streak
    const recentCompletions = await prisma.missionProgress.findMany({
      where: {
        userId,
        isCompleted: true,
        completedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      orderBy: { completedAt: 'desc' }
    })

    // Simple streak calculation (consecutive days with completions)
    const streakCount = calculateSimpleStreak(recentCompletions)

    // Send mission completion notification
    console.log(`📱 Sending mission completion notification for user ${userId}`)

    // In a real implementation, you would call the notification service here
    // For now, we'll just emit a Socket.io event
    const io = require('./socket-instance') // You'd need to export the io instance
    if (io) {
      io.to(`user:${userId}`).emit('mission_notification', {
        type: 'mission_completed',
        title: '🎯 Mission Completed!',
        message: `You've completed "${mission.title}" and earned ${pointsEarned} points!`,
        data: {
          missionId,
          courseId,
          pointsEarned,
          isFirstMission,
          streakCount
        }
      })

      // Send achievement notifications if applicable
      if (isFirstMission) {
        io.to(`user:${userId}`).emit('achievement_notification', {
          type: 'achievement_unlocked',
          title: '🚀 Achievement Unlocked!',
          message: 'Mission Starter - Complete your first mission',
          data: {
            achievementId: 'first_mission',
            rarity: 'common',
            points: 50
          }
        })
      }

      if (streakCount >= 3) {
        io.to(`user:${userId}`).emit('achievement_notification', {
          type: 'streak_achieved',
          title: `🔥 ${streakCount} Mission Streak!`,
          message: `You're on fire! ${streakCount} missions completed in a row!`,
          data: {
            streakCount,
            type: 'mission_streak'
          }
        })
      }
    }

  } catch (error) {
    console.error('Error handling mission completion rewards:', error)
  }
}

// Simple streak calculation helper
function calculateSimpleStreak(completions) {
  if (completions.length === 0) return 0

  let streak = 0
  let currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)

  const completionDates = completions
    .map(c => {
      const date = new Date(c.completedAt)
      date.setHours(0, 0, 0, 0)
      return date.getTime()
    })
    .filter((date, index, arr) => arr.indexOf(date) === index) // Remove duplicates
    .sort((a, b) => b - a) // Sort descending

  for (const completionTime of completionDates) {
    if (completionTime === currentDate.getTime()) {
      streak++
      currentDate.setDate(currentDate.getDate() - 1)
    } else if (completionTime === currentDate.getTime() + 24 * 60 * 60 * 1000) {
      // Allow for yesterday if today has no activity
      streak++
      currentDate.setDate(currentDate.getDate() - 1)
    } else {
      break
    }
  }

  return streak
}

// Start the server
httpServer.listen(port, () => {
  console.log(`🚀 Socket server running on port ${port}`)
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`🔗 CORS origin: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}`)
})

// Log server statistics every 5 minutes
setInterval(() => {
  console.log(`📊 Server stats - Connected users: ${connectedUsers.size}, Active rooms: ${activeRooms.size}`)
}, 5 * 60 * 1000)
