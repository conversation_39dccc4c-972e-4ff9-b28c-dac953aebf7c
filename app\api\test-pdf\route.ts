import { NextRequest, NextResponse } from 'next/server'
import { generateCertificatePDF } from '@/lib/pdf-generator'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing PDF generation...')
    
    const testHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Test Certificate</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
          }
          .certificate {
            background: white;
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
          }
          .title {
            font-size: 48px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
          }
          .subtitle {
            font-size: 24px;
            color: #4a5568;
            margin-bottom: 40px;
          }
          .content {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 40px;
          }
        </style>
      </head>
      <body>
        <div class="certificate">
          <div class="title">Test Certificate</div>
          <div class="subtitle">Certificate of Completion</div>
          <div class="content">
            This is a test certificate to verify that PDF generation is working correctly.
            <br><br>
            Generated on: ${new Date().toLocaleDateString()}
          </div>
        </div>
      </body>
      </html>
    `
    
    const pdfBuffer = await generateCertificatePDF(testHTML)
    
    console.log('PDF generated successfully, size:', pdfBuffer.length, 'bytes')
    
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="test-certificate.pdf"',
        'Content-Length': pdfBuffer.length.toString()
      }
    })
    
  } catch (error) {
    console.error('Test PDF generation error:', error)
    return NextResponse.json(
      { error: 'PDF generation failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
